# src/entities/bot.py

import pygame
import random
import math
from settings import (BOT_SPEED, BOT_MAX_BULLETS, WORLD_WIDTH, WORLD_HEIGHT,
                     BOT_DETECTION_RANGE, BOT_ATTACK_RANGE, BOT_PATROL_RADIUS,
                     BOT_SHOOT_COOLDOWN, BOT_ACCURACY, BOT_PREDICTION_FACTOR,
                     ELITE_BOT_CHANCE, SWARM_BEHAVIOR, BOT_MAX_HEALTH)
from entities.player import Player
from entities.bullet import Bullet

class Bot(Player):
    def __init__(self, x, y, sprite_path, target, bot_list=None):
        """
        x, y: posição inicial do Bot
        sprite_path: caminho do sprite
        target: instância de Player que o Bot vai perseguir/atacar
        bot_list: lista de outros bots para coordenação
        """
        super().__init__(x, y, sprite_path, controls={})
        self.target = target
        self.bot_list = bot_list or []

        # Determina se é um bot elite
        self.is_elite = random.random() < ELITE_BOT_CHANCE

        # Configurações baseadas no tipo
        if self.is_elite:
            self.max_health = BOT_MAX_HEALTH + 50  # Bots elite têm mais vida
            self.health = self.max_health
            self.speed_multiplier = 1.3
            self.accuracy_bonus = 0.1
            self.detection_bonus = 50
            # Cor diferente para bots elite (vermelho mais escuro)
            if hasattr(self, 'image'):
                self.image.fill((150, 0, 0))
        else:
            self.max_health = BOT_MAX_HEALTH
            self.health = BOT_MAX_HEALTH
            self.speed_multiplier = 1.0
            self.accuracy_bonus = 0.0
            self.detection_bonus = 0

        # IA super avançada
        self.state = "patrol"  # patrol, chase, attack, flank, retreat
        self.patrol_target_x = x
        self.patrol_target_y = y
        self.detection_range = BOT_DETECTION_RANGE + self.detection_bonus
        self.attack_range = BOT_ATTACK_RANGE
        self.patrol_radius = BOT_PATROL_RADIUS
        self.direction_change_timer = 0
        self.wander_direction = random.choice([(1, 0), (-1, 0), (0, 1), (0, -1)])

        # Sistemas avançados de IA
        self.player_last_seen_pos = None
        self.player_velocity = (0, 0)
        self.last_player_pos = (target.rect.centerx, target.rect.centery)
        self.prediction_accuracy = BOT_ACCURACY + self.accuracy_bonus
        self.dodge_timer = 0
        self.dodge_direction = (0, 0)
        self.group_coordination_timer = 0
        self.preferred_distance = random.randint(60, 120)  # Distância preferida do alvo
        self.aggression_level = random.uniform(0.7, 1.0)  # Nível de agressividade
        self.retreat_health_threshold = 1  # Recua quando vida baixa

    def get_distance_to_target(self):
        """Calcula a distância até o alvo."""
        dx = self.target.rect.centerx - self.rect.centerx
        dy = self.target.rect.centery - self.rect.centery
        return math.sqrt(dx * dx + dy * dy)

    def update_player_tracking(self):
        """Atualiza o rastreamento do jogador para predição."""
        current_pos = (self.target.rect.centerx, self.target.rect.centery)

        # Calcula velocidade do jogador
        if self.last_player_pos:
            self.player_velocity = (
                current_pos[0] - self.last_player_pos[0],
                current_pos[1] - self.last_player_pos[1]
            )

        self.last_player_pos = current_pos

        # Atualiza última posição vista
        if self.get_distance_to_target() <= self.detection_range:
            self.player_last_seen_pos = current_pos

    def predict_player_position(self, time_ahead=0.5):
        """Prediz onde o jogador estará baseado na velocidade atual."""
        if not self.player_last_seen_pos:
            return self.target.rect.centerx, self.target.rect.centery

        # Predição baseada na velocidade
        predicted_x = self.player_last_seen_pos[0] + (self.player_velocity[0] * time_ahead * BOT_PREDICTION_FACTOR)
        predicted_y = self.player_last_seen_pos[1] + (self.player_velocity[1] * time_ahead * BOT_PREDICTION_FACTOR)

        return predicted_x, predicted_y

    def get_nearby_bots(self, radius=200):
        """Retorna bots próximos para coordenação."""
        nearby = []
        for bot in self.bot_list:
            if bot != self and bot.life > 0:
                dx = bot.rect.centerx - self.rect.centerx
                dy = bot.rect.centery - self.rect.centery
                distance = math.sqrt(dx * dx + dy * dy)
                if distance <= radius:
                    nearby.append(bot)
        return nearby

    def ai_move(self, current_time):
        """IA melhorada e mais agressiva para movimento."""
        self.update_player_tracking()
        distance_to_target = self.get_distance_to_target()

        # Lógica de estado simplificada e mais agressiva
        if self.health <= 20:  # Vida baixa
            self.state = "retreat"
        elif distance_to_target <= 120:  # Bem próximo - ataca
            self.state = "attack"
        elif distance_to_target <= 300:  # Detectou o jogador - persegue
            self.state = "chase"
        else:
            self.state = "patrol"

        # Executa comportamento baseado no estado
        if self.state == "patrol":
            self._patrol_behavior(current_time)
        elif self.state == "chase":
            self._chase_behavior()
        elif self.state == "attack":
            self._attack_behavior(current_time)
        elif self.state == "retreat":
            self._retreat_behavior()

        # Limita movimento aos limites do mundo
        self.rect.x = max(0, min(self.rect.x, WORLD_WIDTH - self.rect.width))
        self.rect.y = max(0, min(self.rect.y, WORLD_HEIGHT - self.rect.height))

    def _patrol_behavior(self, current_time):
        """Comportamento de patrulha inteligente."""
        if current_time - self.direction_change_timer > random.randint(1500, 3000):
            self.wander_direction = random.choice([(1, 0), (-1, 0), (0, 1), (0, -1), (1, 1), (-1, -1), (1, -1), (-1, 1)])
            self.direction_change_timer = current_time

        speed = BOT_SPEED * self.speed_multiplier
        new_x = self.rect.x + self.wander_direction[0] * speed
        new_y = self.rect.y + self.wander_direction[1] * speed

        patrol_distance = math.sqrt((new_x - self.patrol_target_x)**2 + (new_y - self.patrol_target_y)**2)
        if patrol_distance < self.patrol_radius:
            self.rect.x = new_x
            self.rect.y = new_y

    def _chase_behavior(self):
        """Perseguição inteligente com predição."""
        predicted_x, predicted_y = self.predict_player_position()

        dx = predicted_x - self.rect.centerx
        dy = predicted_y - self.rect.centery
        distance = math.sqrt(dx * dx + dy * dy)

        if distance > 0:
            speed = BOT_SPEED * self.speed_multiplier
            self.rect.x += int((dx / distance) * speed)
            self.rect.y += int((dy / distance) * speed)

    def _attack_behavior(self, current_time):
        """Comportamento de ataque simplificado e mais agressivo."""
        dx = self.target.rect.centerx - self.rect.centerx
        dy = self.target.rect.centery - self.rect.centery
        distance = math.sqrt(dx * dx + dy * dy)

        if distance > 0:
            # Movimento mais direto e agressivo
            if distance > 80:  # Se longe, aproxima-se
                speed = BOT_SPEED * self.speed_multiplier * 1.2  # Mais rápido
                self.rect.x += int((dx / distance) * speed)
                self.rect.y += int((dy / distance) * speed)
            else:  # Se próximo, movimento circular simples
                angle = math.atan2(dy, dx) + 0.2  # Rotação mais rápida
                circle_radius = 60  # Raio menor para ser mais agressivo
                target_x = self.target.rect.centerx + math.cos(angle) * circle_radius
                target_y = self.target.rect.centery + math.sin(angle) * circle_radius

                move_dx = target_x - self.rect.centerx
                move_dy = target_y - self.rect.centery
                move_distance = math.sqrt(move_dx * move_dx + move_dy * move_dy)

                if move_distance > 0:
                    speed = BOT_SPEED * self.speed_multiplier
                    self.rect.x += int((move_dx / move_distance) * speed)
                    self.rect.y += int((move_dy / move_distance) * speed)

    def _retreat_behavior(self):
        """Comportamento de recuo quando com pouca vida."""
        dx = self.target.rect.centerx - self.rect.centerx
        dy = self.target.rect.centery - self.rect.centery
        distance = math.sqrt(dx * dx + dy * dy)

        if distance > 0:
            # Move na direção oposta ao jogador
            speed = BOT_SPEED * self.speed_multiplier * 1.5  # Mais rápido na fuga
            self.rect.x -= int((dx / distance) * speed)
            self.rect.y -= int((dy / distance) * speed)

    def ai_shoot(self, current_time):
        """Sistema de tiro melhorado e mais agressivo."""
        # Atira em estados de combate
        if self.state not in ["attack", "chase"]:
            return

        distance_to_target = self.get_distance_to_target()

        # Alcance de tiro mais generoso
        max_range = 200 if self.is_elite else 150

        if distance_to_target <= max_range and distance_to_target > 0:
            # Mira diretamente no jogador (sem predição complexa)
            dx = self.target.rect.centerx - self.rect.centerx
            dy = self.target.rect.centery - self.rect.centery
            distance = math.sqrt(dx * dx + dy * dy)

            if distance > 0:
                # Direção normalizada
                direction = (dx / distance, dy / distance)

                # Cooldown mais agressivo
                cooldown = 400 if self.is_elite else 600  # Mais rápido que antes

                if len(self.bullets) < BOT_MAX_BULLETS and current_time - self.last_shot > cooldown:
                    bx = self.rect.centerx
                    by = self.rect.centery
                    bullet = Bullet(bx, by, direction)
                    self.bullets.append(bullet)
                    self.last_shot = current_time

    def take_damage(self, damage=25):
        """Recebe dano e pode alterar comportamento."""
        self.health -= damage
        self.health = max(0, self.health)

        # Aumenta agressividade quando ferido
        if self.health <= self.retreat_health_threshold * self.max_health:
            self.aggression_level = min(1.0, self.aggression_level + 0.2)

        return self.health <= 0  # Retorna True se morreu

    def update(self, current_time):
        """
        Atualização principal do bot com IA avançada.
        """
        self.ai_move(current_time)
        self.ai_shoot(current_time)
        self.update_bullets()
