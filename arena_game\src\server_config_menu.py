# src/server_config_menu.py

import pygame
import math
import time
from settings import WIDTH, HEIGHT, WHITE, BLACK, UI_BLUE, UI_GREEN, UI_RED, UI_DARK_GRAY
from ui import ui

class ServerConfigMenu:
    def __init__(self):
        """
        Menu para configurar servidor antes de iniciar.
        """
        self.selected_option = 0
        self.game_mode = "pve"  # "pve" ou "pvp"
        self.enable_bots = True
        self.max_players = 4
        self.server_name = "Arena Server"
        self.input_active = False
        self.input_field = None
        self.blink_timer = 0
        
    def handle_events(self, events):
        """Gerencia eventos do menu de configuração."""
        for event in events:
            if event.type == pygame.KEYDOWN:
                if self.input_active:
                    return self._handle_input_keys(event)
                else:
                    return self._handle_navigation_keys(event)
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                return self._handle_mouse_click(event)
        
        return None
    
    def _handle_navigation_keys(self, event):
        """Gerencia teclas de navegação."""
        if event.key == pygame.K_UP:
            self.selected_option = (self.selected_option - 1) % 5
        elif event.key == pygame.K_DOWN:
            self.selected_option = (self.selected_option + 1) % 5
        elif event.key == pygame.K_LEFT or event.key == pygame.K_RIGHT:
            self._toggle_current_option()
        elif event.key == pygame.K_RETURN:
            if self.selected_option == 4:  # Iniciar servidor
                return self._get_server_config()
            else:
                self._toggle_current_option()
        elif event.key == pygame.K_ESCAPE:
            return "back"
        
        return None
    
    def _handle_input_keys(self, event):
        """Gerencia entrada de texto."""
        if event.key == pygame.K_RETURN:
            self.input_active = False
            self.input_field = None
        elif event.key == pygame.K_ESCAPE:
            self.input_active = False
            self.input_field = None
        elif event.key == pygame.K_BACKSPACE:
            if self.input_field == "server_name" and self.server_name:
                self.server_name = self.server_name[:-1]
            elif self.input_field == "max_players" and len(str(self.max_players)) > 1:
                self.max_players = int(str(self.max_players)[:-1]) if str(self.max_players)[:-1] else 2
        elif event.unicode.isprintable():
            if self.input_field == "server_name" and len(self.server_name) < 20:
                self.server_name += event.unicode
            elif self.input_field == "max_players" and event.unicode.isdigit():
                new_value = int(str(self.max_players) + event.unicode)
                if 2 <= new_value <= 8:
                    self.max_players = new_value
        
        return None
    
    def _handle_mouse_click(self, event):
        """Gerencia cliques do mouse."""
        mouse_pos = pygame.mouse.get_pos()
        
        # Opções clicáveis
        option_rects = [
            pygame.Rect(WIDTH//2 - 200, HEIGHT//2 - 120, 400, 40),  # Modo de jogo
            pygame.Rect(WIDTH//2 - 200, HEIGHT//2 - 60, 400, 40),   # Bots IA
            pygame.Rect(WIDTH//2 - 200, HEIGHT//2, 400, 40),        # Nome do servidor
            pygame.Rect(WIDTH//2 - 200, HEIGHT//2 + 60, 400, 40),   # Máximo de jogadores
            pygame.Rect(WIDTH//2 - 100, HEIGHT//2 + 140, 200, 50)   # Iniciar servidor
        ]
        
        for i, rect in enumerate(option_rects):
            if rect.collidepoint(mouse_pos):
                if i == 4:  # Iniciar servidor
                    return self._get_server_config()
                elif i == 2:  # Nome do servidor
                    self.input_active = True
                    self.input_field = "server_name"
                elif i == 3:  # Máximo de jogadores
                    self.input_active = True
                    self.input_field = "max_players"
                else:
                    self.selected_option = i
                    self._toggle_current_option()
                break
        
        # Botão voltar
        back_rect = pygame.Rect(50, HEIGHT - 80, 100, 50)
        if back_rect.collidepoint(mouse_pos):
            return "back"
        
        return None
    
    def _toggle_current_option(self):
        """Alterna a opção selecionada."""
        if self.selected_option == 0:  # Modo de jogo
            self.game_mode = "pvp" if self.game_mode == "pve" else "pve"
            # Ajusta bots automaticamente
            if self.game_mode == "pvp":
                self.enable_bots = False
        elif self.selected_option == 1:  # Bots IA
            if self.game_mode == "pve":  # Só permite bots em PvE
                self.enable_bots = not self.enable_bots
    
    def _get_server_config(self):
        """Retorna configuração do servidor."""
        return {
            "action": "start_server",
            "game_mode": self.game_mode,
            "enable_bots": self.enable_bots,
            "max_players": self.max_players,
            "server_name": self.server_name
        }
    
    def draw(self, surface):
        """Desenha o menu de configuração."""
        # Fundo moderno
        self._draw_modern_background(surface)
        
        # Título
        self._draw_title(surface)
        
        # Opções de configuração
        self._draw_config_options(surface)
        
        # Botões de ação
        self._draw_action_buttons(surface)
        
        # Efeitos visuais
        self._draw_visual_effects(surface)
    
    def _draw_modern_background(self, surface):
        """Desenha fundo moderno."""
        # Gradiente
        for y in range(HEIGHT):
            color_factor = y / HEIGHT
            r = int(25 + color_factor * 20)
            g = int(30 + color_factor * 25)
            b = int(40 + color_factor * 30)
            pygame.draw.line(surface, (r, g, b), (0, y), (WIDTH, y))
        
        # Grade hexagonal
        hex_color = (50, 60, 75)
        for x in range(0, WIDTH, 80):
            for y in range(0, HEIGHT, 70):
                offset_x = 40 if (y // 70) % 2 else 0
                self._draw_hexagon(surface, x + offset_x, y, 30, hex_color)
    
    def _draw_hexagon(self, surface, x, y, size, color):
        """Desenha hexágono."""
        points = []
        for i in range(6):
            angle = math.pi / 3 * i
            px = x + size * math.cos(angle)
            py = y + size * math.sin(angle)
            points.append((px, py))
        pygame.draw.polygon(surface, color, points, 1)
    
    def _draw_title(self, surface):
        """Desenha título."""
        # Sombra
        ui.draw_text(surface, "CONFIGURAÇÃO DO SERVIDOR", ui.font_title, (10, 10, 10),
                    WIDTH//2 + 3, 83, center=True)
        
        # Título principal
        ui.draw_text(surface, "CONFIGURAÇÃO DO SERVIDOR", ui.font_title, WHITE,
                    WIDTH//2, 80, center=True)
        
        # Subtítulo
        ui.draw_text(surface, "DEFINA AS REGRAS DA BATALHA", ui.font_large, UI_BLUE,
                    WIDTH//2, 120, center=True)
        
        # Linha decorativa
        pygame.draw.line(surface, UI_BLUE, (WIDTH//2 - 200, 140), (WIDTH//2 + 200, 140), 3)
    
    def _draw_config_options(self, surface):
        """Desenha opções de configuração."""
        options = [
            ("MODO DE JOGO", self._get_game_mode_text()),
            ("BOTS IA", "Habilitados" if self.enable_bots else "Desabilitados"),
            ("NOME DO SERVIDOR", self.server_name),
            ("MÁXIMO DE JOGADORES", str(self.max_players))
        ]
        
        for i, (label, value) in enumerate(options):
            y = HEIGHT//2 - 120 + i * 60
            
            # Fundo da opção
            option_rect = pygame.Rect(WIDTH//2 - 200, y, 400, 40)
            
            if i == self.selected_option:
                pygame.draw.rect(surface, (60, 80, 100), option_rect)
                pygame.draw.rect(surface, UI_BLUE, option_rect, 3)
            else:
                pygame.draw.rect(surface, (40, 50, 65), option_rect)
                pygame.draw.rect(surface, (80, 90, 110), option_rect, 2)
            
            # Label
            ui.draw_text(surface, label, ui.font_normal, WHITE,
                        WIDTH//2 - 180, y + 10, center=False)
            
            # Valor
            value_color = UI_GREEN if i == self.selected_option else (200, 200, 200)
            
            # Campo de entrada ativo
            if self.input_active and ((i == 2 and self.input_field == "server_name") or 
                                    (i == 3 and self.input_field == "max_players")):
                # Cursor piscante
                self.blink_timer += 1
                if self.blink_timer % 60 < 30:
                    value += "|"
                value_color = UI_BLUE
            
            ui.draw_text(surface, value, ui.font_normal, value_color,
                        WIDTH//2 + 180, y + 10, center=True)
            
            # Indicador de que pode ser alterado
            if i < 2:  # Modo e bots
                ui.draw_text(surface, "← →", ui.font_small, (150, 150, 150),
                            WIDTH//2 + 180, y + 30, center=True)
            elif i >= 2:  # Campos de texto
                ui.draw_text(surface, "Clique para editar", ui.font_small, (150, 150, 150),
                            WIDTH//2 + 180, y + 30, center=True)
    
    def _draw_action_buttons(self, surface):
        """Desenha botões de ação."""
        # Botão iniciar servidor
        start_rect = pygame.Rect(WIDTH//2 - 100, HEIGHT//2 + 140, 200, 50)
        
        if self.selected_option == 4:
            pygame.draw.rect(surface, (60, 150, 60), start_rect)
            pygame.draw.rect(surface, UI_GREEN, start_rect, 3)
        else:
            pygame.draw.rect(surface, (50, 120, 50), start_rect)
            pygame.draw.rect(surface, (100, 200, 100), start_rect, 2)
        
        ui.draw_text(surface, "INICIAR SERVIDOR", ui.font_normal, WHITE,
                    start_rect.centerx, start_rect.centery, center=True)
        
        # Botão voltar
        back_rect = pygame.Rect(50, HEIGHT - 80, 100, 50)
        pygame.draw.rect(surface, (150, 50, 50), back_rect)
        pygame.draw.rect(surface, UI_RED, back_rect, 3)
        ui.draw_text(surface, "VOLTAR", ui.font_normal, WHITE,
                    back_rect.centerx, back_rect.centery, center=True)
        
        # Instruções
        ui.draw_text(surface, "↑↓ para navegar | ←→ para alterar | ENTER para confirmar",
                    ui.font_small, (150, 150, 170), WIDTH//2, HEIGHT - 30, center=True)
    
    def _draw_visual_effects(self, surface):
        """Desenha efeitos visuais."""
        current_time = time.time()
        
        # Partículas
        for i in range(4):
            x = 150 + i * 200 + math.sin(current_time + i) * 40
            y = 250 + math.cos(current_time * 0.7 + i) * 30
            size = 3 + math.sin(current_time * 2 + i) * 1.5
            
            color = (80, 120, 160)
            pygame.draw.circle(surface, color, (int(x), int(y)), int(size))
    
    def _get_game_mode_text(self):
        """Retorna texto do modo de jogo."""
        if self.game_mode == "pve":
            return "PvE Cooperativo"
        else:
            return "PvP Competitivo"

# Instância global
server_config_menu = ServerConfigMenu()
