# src/visual_effects.py

import pygame
import math
import random
from settings import WIDTH, HEIGHT

class ParticleSystem:
    def __init__(self):
        """
        Sistema de partículas para efeitos visuais modernos.
        """
        self.particles = []
        self.muzzle_flashes = []
        self.impact_effects = []
        
    def add_muzzle_flash(self, x, y, direction):
        """
        Adiciona efeito de disparo moderno.
        """
        flash = {
            'x': x,
            'y': y,
            'direction': direction,
            'life': 8,  # frames
            'size': random.randint(8, 12)
        }
        self.muzzle_flashes.append(flash)
        
        # Adiciona partículas de fumaça
        for i in range(3):
            particle = {
                'x': x + random.randint(-5, 5),
                'y': y + random.randint(-5, 5),
                'vx': direction[0] * random.randint(1, 3) + random.uniform(-1, 1),
                'vy': direction[1] * random.randint(1, 3) + random.uniform(-1, 1),
                'life': random.randint(15, 25),
                'color': (200, 200, 200),
                'size': random.randint(2, 4)
            }
            self.particles.append(particle)
    
    def add_impact_effect(self, x, y):
        """
        Adiciona efeito de impacto de bala.
        """
        impact = {
            'x': x,
            'y': y,
            'life': 12,
            'size': random.randint(6, 10)
        }
        self.impact_effects.append(impact)
        
        # Adiciona partículas de impacto
        for i in range(5):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(2, 5)
            particle = {
                'x': x,
                'y': y,
                'vx': math.cos(angle) * speed,
                'vy': math.sin(angle) * speed,
                'life': random.randint(10, 20),
                'color': (255, 200, 100),
                'size': random.randint(1, 3)
            }
            self.particles.append(particle)
    
    def update(self):
        """
        Atualiza todos os efeitos visuais.
        """
        # Atualiza partículas
        for particle in self.particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['life'] -= 1
            particle['vx'] *= 0.98  # Resistência do ar
            particle['vy'] *= 0.98
            
            if particle['life'] <= 0:
                self.particles.remove(particle)
        
        # Atualiza flashes de disparo
        for flash in self.muzzle_flashes[:]:
            flash['life'] -= 1
            if flash['life'] <= 0:
                self.muzzle_flashes.remove(flash)
        
        # Atualiza efeitos de impacto
        for impact in self.impact_effects[:]:
            impact['life'] -= 1
            if impact['life'] <= 0:
                self.impact_effects.remove(impact)
    
    def draw(self, surface, camera=None):
        """
        Desenha todos os efeitos visuais.
        """
        # Desenha partículas
        for particle in self.particles:
            if camera:
                screen_x = particle['x'] - camera.x
                screen_y = particle['y'] - camera.y
                if 0 <= screen_x <= WIDTH and 0 <= screen_y <= HEIGHT:
                    alpha = int(255 * (particle['life'] / 25))
                    color = (*particle['color'], min(alpha, 255))
                    pygame.draw.circle(surface, color[:3], (int(screen_x), int(screen_y)), particle['size'])
            else:
                alpha = int(255 * (particle['life'] / 25))
                color = (*particle['color'], min(alpha, 255))
                pygame.draw.circle(surface, color[:3], (int(particle['x']), int(particle['y'])), particle['size'])
        
        # Desenha flashes de disparo
        for flash in self.muzzle_flashes:
            if camera:
                screen_x = flash['x'] - camera.x
                screen_y = flash['y'] - camera.y
                if 0 <= screen_x <= WIDTH and 0 <= screen_y <= HEIGHT:
                    alpha = int(255 * (flash['life'] / 8))
                    color = (255, 255, 100, alpha)
                    pygame.draw.circle(surface, color[:3], (int(screen_x), int(screen_y)), flash['size'])
            else:
                alpha = int(255 * (flash['life'] / 8))
                color = (255, 255, 100, alpha)
                pygame.draw.circle(surface, color[:3], (int(flash['x']), int(flash['y'])), flash['size'])
        
        # Desenha efeitos de impacto
        for impact in self.impact_effects:
            if camera:
                screen_x = impact['x'] - camera.x
                screen_y = impact['y'] - camera.y
                if 0 <= screen_x <= WIDTH and 0 <= screen_y <= HEIGHT:
                    alpha = int(255 * (impact['life'] / 12))
                    color = (255, 100, 100, alpha)
                    pygame.draw.circle(surface, color[:3], (int(screen_x), int(screen_y)), impact['size'])
            else:
                alpha = int(255 * (impact['life'] / 12))
                color = (255, 100, 100, alpha)
                pygame.draw.circle(surface, color[:3], (int(impact['x']), int(impact['y'])), impact['size'])

class ModernBullet:
    def __init__(self, x, y, direction, bullet_type="standard"):
        """
        Bala moderna com efeitos visuais aprimorados.
        """
        self.x = x
        self.y = y
        self.direction = direction
        self.bullet_type = bullet_type
        self.speed = 12
        self.trail = []  # Rastro da bala
        
        # Propriedades visuais baseadas no tipo
        if bullet_type == "energy":
            self.color = (100, 200, 255)
            self.size = 3
            self.trail_length = 8
        elif bullet_type == "explosive":
            self.color = (255, 150, 50)
            self.size = 4
            self.trail_length = 6
        else:  # standard
            self.color = (255, 255, 150)
            self.size = 2
            self.trail_length = 5
        
        self.rect = pygame.Rect(x-1, y-1, 3, 3)
    
    def update(self):
        """
        Atualiza posição da bala e rastro.
        """
        # Adiciona posição atual ao rastro
        self.trail.append((self.x, self.y))
        if len(self.trail) > self.trail_length:
            self.trail.pop(0)
        
        # Move a bala
        self.x += self.direction[0] * self.speed
        self.y += self.direction[1] * self.speed
        self.rect.center = (self.x, self.y)
    
    def draw(self, surface, camera=None):
        """
        Desenha a bala com rastro moderno.
        """
        if camera:
            screen_x = self.x - camera.x
            screen_y = self.y - camera.y
            
            # Desenha rastro
            for i, (trail_x, trail_y) in enumerate(self.trail):
                trail_screen_x = trail_x - camera.x
                trail_screen_y = trail_y - camera.y
                alpha = int(255 * (i / len(self.trail)))
                trail_color = (*self.color, alpha)
                trail_size = max(1, self.size - (len(self.trail) - i))
                pygame.draw.circle(surface, trail_color[:3], (int(trail_screen_x), int(trail_screen_y)), trail_size)
            
            # Desenha bala principal
            pygame.draw.circle(surface, self.color, (int(screen_x), int(screen_y)), self.size)
            
            # Efeito de brilho para balas de energia
            if self.bullet_type == "energy":
                glow_color = (self.color[0]//2, self.color[1]//2, self.color[2]//2)
                pygame.draw.circle(surface, glow_color, (int(screen_x), int(screen_y)), self.size + 2)
        else:
            # Desenha rastro
            for i, (trail_x, trail_y) in enumerate(self.trail):
                alpha = int(255 * (i / len(self.trail)))
                trail_color = (*self.color, alpha)
                trail_size = max(1, self.size - (len(self.trail) - i))
                pygame.draw.circle(surface, trail_color[:3], (int(trail_x), int(trail_y)), trail_size)
            
            # Desenha bala principal
            pygame.draw.circle(surface, self.color, (int(self.x), int(self.y)), self.size)

class HealthBar:
    def __init__(self, max_health):
        """
        Barra de vida moderna e estilizada.
        """
        self.max_health = max_health
        
    def draw(self, surface, x, y, current_health, width=100, height=8):
        """
        Desenha barra de vida moderna com gradiente.
        """
        # Fundo da barra
        bg_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(surface, (40, 40, 40), bg_rect)
        pygame.draw.rect(surface, (100, 100, 100), bg_rect, 1)
        
        # Calcula porcentagem de vida
        health_percent = current_health / self.max_health
        health_width = int(width * health_percent)
        
        if health_width > 0:
            # Cor baseada na porcentagem de vida
            if health_percent > 0.6:
                color = (50, 255, 50)  # Verde
            elif health_percent > 0.3:
                color = (255, 255, 50)  # Amarelo
            else:
                color = (255, 50, 50)  # Vermelho
            
            # Barra de vida
            health_rect = pygame.Rect(x + 1, y + 1, health_width - 2, height - 2)
            pygame.draw.rect(surface, color, health_rect)
            
            # Efeito de brilho
            glow_rect = pygame.Rect(x + 1, y + 1, health_width - 2, 2)
            glow_color = (min(255, color[0] + 50), min(255, color[1] + 50), min(255, color[2] + 50))
            pygame.draw.rect(surface, glow_color, glow_rect)

# Instância global do sistema de partículas
particle_system = ParticleSystem()
