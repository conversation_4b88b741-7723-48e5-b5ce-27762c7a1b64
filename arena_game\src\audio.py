# src/audio.py

import pygame
import os
from settings import ENABLE_SOUND, SOUND_VOLUME, SHOOT_SOUND_FILE

class AudioManager:
    def __init__(self):
        """
        Inicializa o sistema de áudio do jogo.
        """
        self.sounds = {}
        self.enabled = ENABLE_SOUND
        
        if self.enabled:
            try:
                # Inicializa o mixer do pygame
                pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
                pygame.mixer.init()
                
                # Carrega os sons
                self._load_sounds()
                
                print("Sistema de áudio inicializado com sucesso!")
                
            except pygame.error as e:
                print(f"Erro ao inicializar áudio: {e}")
                self.enabled = False
        else:
            print("Sistema de áudio desabilitado.")
    
    def _load_sounds(self):
        """
        Carrega todos os arquivos de som.
        """
        sounds_dir = os.path.join("..", "sounds")
        
        # Carrega som do tiro
        shoot_sound_path = os.path.join(sounds_dir, SHOOT_SOUND_FILE)
        
        try:
            if os.path.exists(shoot_sound_path):
                self.sounds['shoot'] = pygame.mixer.Sound(shoot_sound_path)
                self.sounds['shoot'].set_volume(SOUND_VOLUME)
                print(f"Som do tiro carregado: {SHOOT_SOUND_FILE}")
            else:
                print(f"Arquivo de som não encontrado: {shoot_sound_path}")
                
        except pygame.error as e:
            print(f"Erro ao carregar som do tiro: {e}")
    
    def play_shoot_sound(self):
        """
        Toca o som do tiro.
        """
        if self.enabled and 'shoot' in self.sounds:
            try:
                self.sounds['shoot'].play()
            except pygame.error as e:
                print(f"Erro ao tocar som do tiro: {e}")
    
    def set_volume(self, volume):
        """
        Ajusta o volume de todos os sons.
        volume: float entre 0.0 e 1.0
        """
        if self.enabled:
            for sound in self.sounds.values():
                sound.set_volume(volume)
    
    def toggle_sound(self):
        """
        Liga/desliga o som.
        """
        self.enabled = not self.enabled
        if not self.enabled:
            pygame.mixer.stop()
        return self.enabled
    
    def stop_all_sounds(self):
        """
        Para todos os sons que estão tocando.
        """
        if self.enabled:
            pygame.mixer.stop()

# Instância global do gerenciador de áudio
audio_manager = AudioManager()
