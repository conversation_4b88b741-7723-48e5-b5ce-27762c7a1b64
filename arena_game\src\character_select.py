# src/character_select.py

import pygame
from settings import WIDTH, HEIGHT, WHITE, BLACK, UI_BLUE, UI_GREEN, UI_RED, UI_DARK_GRAY, UI_LIGHT_GRAY
from ui import ui
from characters import CHARACTERS, get_character_list

class CharacterSelect:
    def __init__(self):
        """
        Tela de seleção de personagens.
        """
        self.selected_character = 0
        self.character_ids = get_character_list()
        self.characters = list(CHARACTERS.values())

    def handle_events(self, events):
        """
        Manipula eventos da tela de seleção.
        """
        for event in events:
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_LEFT:
                    self.selected_character = (self.selected_character - 1) % len(self.characters)
                elif event.key == pygame.K_RIGHT:
                    self.selected_character = (self.selected_character + 1) % len(self.characters)
                elif event.key == pygame.K_RETURN:
                    return self.character_ids[self.selected_character]
                elif event.key == pygame.K_ESCAPE:
                    return "back"

            elif event.type == pygame.MOUSEBUTTONDOWN:
                mouse_pos = pygame.mouse.get_pos()

                # Verifica clique nos botões de navegação
                left_button = pygame.Rect(50, HEIGHT//2 - 25, 50, 50)
                right_button = pygame.Rect(WIDTH - 100, HEIGHT//2 - 25, 50, 50)
                select_button = pygame.Rect(WIDTH//2 - 100, HEIGHT - 100, 200, 50)
                back_button = pygame.Rect(50, HEIGHT - 100, 100, 50)

                if left_button.collidepoint(mouse_pos):
                    self.selected_character = (self.selected_character - 1) % len(self.characters)
                elif right_button.collidepoint(mouse_pos):
                    self.selected_character = (self.selected_character + 1) % len(self.characters)
                elif select_button.collidepoint(mouse_pos):
                    return self.character_ids[self.selected_character]
                elif back_button.collidepoint(mouse_pos):
                    return "back"

        return None

    def draw(self, surface):
        """
        Desenha a tela de seleção de personagens.
        """
        # Fundo
        surface.fill(UI_DARK_GRAY)

        # Título
        ui.draw_text(surface, "SELEÇÃO DE PERSONAGEM", ui.font_title, WHITE,
                    WIDTH//2, 50, center=True)

        # Personagem atual
        current_char = self.characters[self.selected_character]

        # Painel central do personagem
        panel_rect = pygame.Rect(WIDTH//2 - 200, HEIGHT//2 - 150, 400, 300)
        pygame.draw.rect(surface, UI_LIGHT_GRAY, panel_rect)
        pygame.draw.rect(surface, WHITE, panel_rect, 3)

        # Sprite do personagem (ampliado)
        char_sprite = pygame.transform.scale(current_char.sprite, (80, 80))
        sprite_rect = char_sprite.get_rect(center=(WIDTH//2, HEIGHT//2 - 80))
        surface.blit(char_sprite, sprite_rect)

        # Nome do personagem
        ui.draw_text(surface, current_char.name, ui.font_large, BLACK,
                    WIDTH//2, HEIGHT//2 - 20, center=True)

        # Descrição
        ui.draw_text(surface, current_char.description, ui.font_normal, UI_DARK_GRAY,
                    WIDTH//2, HEIGHT//2 + 10, center=True)

        # Estatísticas
        stats_y = HEIGHT//2 + 40
        stats_text = [
            f"Velocidade: {current_char.stats['speed']}/7",
            f"Vida: {current_char.stats['health']}",
            f"Dano: {current_char.stats['damage']}"
        ]

        for i, stat in enumerate(stats_text):
            ui.draw_text(surface, stat, ui.font_small, BLACK,
                        WIDTH//2, stats_y + i * 20, center=True)

        # Habilidade especial
        special_abilities = {
            "tactical": "🎯 Tático",
            "cyber": "🤖 Cibernético",
            "armor": "🛡️ Armadura Pesada",
            "stealth": "🥷 Furtividade",
            "healing": "❤️ Cura",
            "explosives": "💥 Explosivos"
        }

        for ability, description in special_abilities.items():
            if current_char.stats.get(ability):
                ui.draw_text(surface, f"Especial: {description}", ui.font_small, UI_BLUE,
                            WIDTH//2, stats_y + 80, center=True)
                break

        # Botões de navegação
        # Seta esquerda
        ui.draw_button(surface, "◀", 50, HEIGHT//2 - 25, 50, 50, UI_BLUE, WHITE)

        # Seta direita
        ui.draw_button(surface, "▶", WIDTH - 100, HEIGHT//2 - 25, 50, 50, UI_BLUE, WHITE)

        # Indicador de posição
        indicator_text = f"{self.selected_character + 1}/{len(self.characters)}"
        ui.draw_text(surface, indicator_text, ui.font_small, WHITE,
                    WIDTH//2, HEIGHT//2 + 130, center=True)

        # Botões inferiores
        ui.draw_button(surface, "VOLTAR", 50, HEIGHT - 100, 100, 50, UI_RED, WHITE)
        ui.draw_button(surface, "SELECIONAR", WIDTH//2 - 100, HEIGHT - 100, 200, 50, UI_GREEN, WHITE)

        # Instruções
        instructions = [
            "← → ou clique nas setas para navegar",
            "ENTER ou clique em SELECIONAR para escolher",
            "ESC para voltar ao menu"
        ]

        for i, instruction in enumerate(instructions):
            ui.draw_text(surface, instruction, ui.font_small, UI_LIGHT_GRAY,
                        WIDTH//2, HEIGHT - 50 + i * 15, center=True)

        # Preview de outros personagens (miniaturas)
        preview_y = 100
        preview_spacing = 80
        start_x = WIDTH//2 - (len(self.characters) * preview_spacing) // 2

        for i, char in enumerate(self.characters):
            x = start_x + i * preview_spacing

            # Destaque do personagem selecionado
            if i == self.selected_character:
                pygame.draw.circle(surface, UI_GREEN, (x, preview_y), 35, 3)

            # Sprite pequeno
            small_sprite = pygame.transform.scale(char.sprite, (40, 40))
            sprite_rect = small_sprite.get_rect(center=(x, preview_y))
            surface.blit(small_sprite, sprite_rect)

            # Nome abreviado
            short_name = char.name.split()[0]  # Primeira palavra
            ui.draw_text(surface, short_name, ui.font_small, WHITE,
                        x, preview_y + 30, center=True)

# Instância global
character_select = CharacterSelect()
