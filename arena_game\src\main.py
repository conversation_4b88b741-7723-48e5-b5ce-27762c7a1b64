# src/main.py

import pygame
import sys
from settings import WIDTH, HEIGHT, FPS, WHITE
from entities.player import Player
from entities.bot import Bo<PERSON>
from utils import draw_text

def main():
    pygame.init()
    screen = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("Arena Multiplayer (MVP)")
    clock = pygame.time.Clock()

    # --- Instanciação dos jogadores (Player 1 e Player 2 local) ---
    # Player 1 (seta WASD + espaço)
    p1 = Player(
        x=100,
        y=HEIGHT // 2 - 20,
        sprite_path=os.path.join("assets", "player1.png"),
        controls={
            'up': pygame.K_w,
            'down': pygame.K_s,
            'left': pygame.K_a,
            'right': pygame.K_d,
            'shoot': pygame.K_SPACE,
            'bullet_dir': (1, 0)   # dispara para a direita
        }
    )

    # Player 2 (setas → + Enter)
    p2 = Player(
        x=WIDTH - 140,
        y=HEIGHT // 2 - 20,
        sprite_path=os.path.join("assets", "player2.png"),
        controls={
            'up': pygame.K_UP,
            'down': pygame.K_DOWN,
            'left': pygame.K_LEFT,
            'right': pygame.K_RIGHT,
            'shoot': pygame.K_RETURN,
            'bullet_dir': (-1, 0)  # dispara para a esquerda
        }
    )

    running = True
    winner = None

    while running:
        clock.tick(FPS)
        current_time = pygame.time.get_ticks()

        # --- Eventos de fechamento ---
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False

        # --- Captura estado atual do teclado ---
        keys = pygame.key.get_pressed()

        # --- Movimentação / Disparo Players ---
        p1.handle_movement(keys)
        p2.handle_movement(keys)
        p1.handle_shooting(keys, current_time)
        p2.handle_shooting(keys, current_time)

        # --- Atualiza Balas ---
        p1.update_bullets()
        p2.update_bullets()

        # --- Checa Colisões: balas de p1 atingindo p2 ---
        for bullet in p1.bullets[:]:
            if bullet.rect.colliderect(p2.rect):
                p1.bullets.remove(bullet)
                p2.life -= 1

        # --- Checa Colisões: balas de p2 atingindo p1 ---
        for bullet in p2.bullets[:]:
            if bullet.rect.colliderect(p1.rect):
                p2.bullets.remove(bullet)
                p1.life -= 1

        # --- Verifica condição de vitória ---
        if p1.life <= 0:
            winner = "Jogador 2"
            running = False
        elif p2.life <= 0:
            winner = "Jogador 1"
            running = False

        # --- Desenho na Tela ---
        screen.fill(WHITE)
        p1.draw(screen)
        p2.draw(screen)

        # Desenha barras de vida/indicadores
        draw_text(screen, f"P1 Life: {p1.life}", 20, 10, 10)
        draw_text(screen, f"P2 Life: {p2.life}", 20, WIDTH - 150, 10)

        pygame.display.flip()

    # --- Tela de vitória e espera por reinício ou saída ---
    screen.fill(WHITE)
    draw_text(screen, f"{winner} venceu!", 40, WIDTH // 2 - 120, HEIGHT // 2 - 20)
    draw_text(screen, "Pressione R para reiniciar ou Q para sair", 24, WIDTH // 2 - 200, HEIGHT // 2 + 30)
    pygame.display.flip()

    waiting = True
    while waiting:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                waiting = False
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r:
                    main()  # reinicia o jogo recursivamente
                    return
                if event.key == pygame.K_q:
                    waiting = False

    pygame.quit()
    sys.exit()


if __name__ == "__main__":
    main()
