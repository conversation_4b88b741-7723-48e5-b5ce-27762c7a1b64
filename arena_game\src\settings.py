# src/settings.py

# --- <PERSON><PERSON><PERSON> da janela ---
WIDTH, HEIGHT = 800, 600

# --- <PERSON><PERSON><PERSON> do mundo/mapa ---
WORLD_WIDTH, WORLD_HEIGHT = 2400, 1800  # Mapa 3x maior

# --- Frames por segundo ---
FPS = 60

# --- Cores (R, G, B) ---
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
GRAY = (128, 128, 128)

# --- Velocidades ---
PLAYER_SPEED = 4  # Reduzido para dar mais vantagem aos bots
BULLET_SPEED = 12  # Aumentado para balas mais rápidas
BOT_SPEED = 3  # Mantido, mas com IA mais inteligente

# --- Dimensões dos sprites ---
PLAYER_WIDTH, PLAYER_HEIGHT = 40, 40
BULLET_WIDTH, BULLET_HEIGHT = 10, 10

# --- Má<PERSON><PERSON> de tiros simultâneos por jogador ---
MAX_BULLETS = 3  # Reduzido para limitar spam de tiros
BOT_MAX_BULLETS = 4  # Bots podem atirar mais

# --- Vida (número de “vidas” ou pontos de vida) inicial de cada jogador ---
PLAYER_LIFE = 5  # Aumentado para compensar dificuldade
BOT_LIFE = 3  # Aumentado para serem mais resistentes

# --- Configurações dos inimigos (DIFICULDADE AUMENTADA) ---
NUM_BOTS = 8  # Mais inimigos
BOT_DETECTION_RANGE = 250  # Detectam de mais longe
BOT_ATTACK_RANGE = 180  # Atacam de mais longe
BOT_PATROL_RADIUS = 150  # Patrulham área maior
BOT_SHOOT_COOLDOWN = 600  # Atiram mais frequentemente (era 800)
BOT_ACCURACY = 0.85  # 85% de precisão nos tiros
BOT_PREDICTION_FACTOR = 0.7  # Preveem movimento do jogador

# --- Configurações da câmera ---
CAMERA_SMOOTHNESS = 0.1  # Suavidade do movimento da câmera (0.1 = mais suave)

# --- Configurações de dificuldade ---
DIFFICULTY_SCALING = True  # Inimigos ficam mais difíceis com o tempo
ELITE_BOT_CHANCE = 0.3  # 30% chance de spawnar bot elite
SWARM_BEHAVIOR = True  # Bots se coordenam em grupos

# --- Configurações de áudio ---
ENABLE_SOUND = True  # Habilita/desabilita sons
SOUND_VOLUME = 0.7  # Volume dos efeitos sonoros (0.0 a 1.0)
SHOOT_SOUND_FILE = "Voicy_Valorant Weapon Ares No Spray .mp3"  # Nome do arquivo de som do tiro
