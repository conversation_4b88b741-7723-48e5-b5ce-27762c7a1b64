# Arena Battle Royale - Changelog

## Versão 2.2 - DIFICULDADE EXTREMA 🔥

### ⚡ **ATENÇÃO: JOGO SUPER DIFÍCIL AGORA!**

#### 🤖 **IA dos Inimigos Completamente Reescrita**
- **8 inimigos** em vez de 5 (mais ação!)
- **Bots Elite** (30% de chance): Mais vida, mais rápidos, mais precisos
- **5 Estados de IA**: Patrulha → Perseguição → Ataque → Flanqueamento → Recuo
- **Predição de movimento**: Bots preveem onde você estará e atiram lá
- **Sistema de esquiva**: Bots esquivam das suas balas ativamente
- **Coordenação em grupo**: Bots se comunicam e flanqueiam em equipe

#### 🎯 **Sistema de Combate Avançado**
- **Tiro preditivo**: Inimigos atiram onde você VAI estar, não onde está
- **Precisão de 85%**: Bots raramente erram (bots elite são ainda melhores)
- **Cooldown reduzido**: Inimigos atiram mais frequentemente
- **4 balas simultâneas** por bot (vs suas 3)

#### 🏃 **Balanceamento de Dificuldade**
- **Sua velocidade reduzida**: 4 (era 5) - você é mais lento
- **Sua vida aumentada**: 5 (era 3) - para compensar a dificuldade
- **Vida dos bots**: 3 (era 2) - mais resistentes
- **Bots elite**: 5 vidas - mini-bosses

#### 🧠 **Comportamentos Inteligentes**
- **Patrulha**: Movimento aleatório inteligente
- **Perseguição**: Seguem sua posição prevista
- **Ataque**: Mantêm distância ideal e circulam
- **Flanqueamento**: Coordenam ataques em grupo
- **Recuo**: Fogem quando feridos para se reagrupar

#### 🎮 **Interface Melhorada**
- **Contador de bots elite**: Mostra quantos elite estão vivos
- **Estados dos bots**: Vê o que bots próximos estão fazendo
- **Minimapa atualizado**: Bots elite aparecem maiores e dourados

## Versão 2.1 - Tiro Direcional

### 🎯 **Nova Funcionalidade: Tiro Direcional**
- **Sistema de tiro baseado no movimento**: As balas agora seguem a direção do seu movimento
- **8 direções de tiro**: Cima, baixo, esquerda, direita e todas as diagonais
- **Memória de direção**: Se você parar de se mover, continua atirando na última direção
- **Controle intuitivo**: Simplesmente mova na direção desejada e atire

## Versão 2.0 - Mundo Expandido

### 🎮 Novas Funcionalidades

#### **Mapa Maior**
- Mundo expandido de 800x600 para 2400x1800 (3x maior)
- Fundo com grade visual para melhor orientação
- Elementos decorativos espalhados pelo mapa
- Bordas visíveis do mundo

#### **Sistema de Câmera**
- Câmera que segue o jogador automaticamente
- Movimento suave da câmera (configurável)
- Otimização: só desenha objetos visíveis na tela
- Limites da câmera respeitam as bordas do mundo

#### **Inimigos Inteligentes (Bots)**
- 5 inimigos espalhados aleatoriamente pelo mapa
- IA com 3 estados: Patrulha, Perseguição e Ataque
- **Patrulha**: Movimento aleatório em área limitada
- **Perseguição**: Detecta jogador a 200 pixels e persegue
- **Ataque**: Circula o jogador e atira quando próximo
- Cada bot tem 2 vidas
- Velocidade diferente dos jogadores (mais lentos)

#### **Interface Melhorada**
- Minimapa no canto superior direito
- Mostra posição do jogador (verde) e inimigos (vermelho)
- Área visível da câmera destacada no minimapa
- Contador de inimigos derrotados
- Indicador de vida do jogador
- Coordenadas de posição (debug)

#### **Sistema de Vitória/Derrota**
- **Vitória**: Derrote todos os 5 inimigos
- **Derrota**: Sua vida chega a zero
- Tela final mostra estatísticas
- Opção de reiniciar (R) ou sair (Q)

### 🎯 Controles
- **WASD**: Movimento do jogador
- **Espaço**: Atirar na direção do movimento
- **R**: Reiniciar jogo (na tela final)
- **Q**: Sair do jogo (na tela final)

### 🎯 Sistema de Tiro Direcional
- As balas agora são disparadas na direção que você está se movendo
- Se você estiver se movendo para cima (W), as balas vão para cima
- Se você estiver se movendo na diagonal (W+D), as balas vão na diagonal
- Se você parar de se mover, as balas continuam na última direção de movimento
- Permite tiro em 8 direções: ↑ ↗ → ↘ ↓ ↙ ← ↖

### 🔧 Melhorias Técnicas
- Sistema de coordenadas do mundo vs. coordenadas da tela
- Otimização de renderização com culling
- Estrutura modular com classes separadas
- Sistema de câmera independente
- IA comportamental para inimigos

### 📁 Estrutura de Arquivos
```
arena_game/
├── src/
│   ├── main.py          # Loop principal do jogo
│   ├── settings.py      # Configurações globais
│   ├── camera.py        # Sistema de câmera
│   ├── world.py         # Mundo/mapa do jogo
│   ├── utils.py         # Funções utilitárias
│   └── entities/
│       ├── player.py    # Classe do jogador
│       ├── bot.py       # IA dos inimigos
│       └── bullet.py    # Sistema de projéteis
└── assets/              # Sprites e recursos
```

### 🎮 Como Jogar
1. Execute `python main.py` na pasta `arena_game/src/`
2. Use WASD para se mover pelo mundo
3. Use Espaço para atirar nos inimigos
4. Observe o minimapa para localizar inimigos
5. Derrote todos os 5 inimigos para vencer!

### ⚙️ Configurações Personalizáveis
No arquivo `settings.py` você pode ajustar:
- Tamanho do mundo (`WORLD_WIDTH`, `WORLD_HEIGHT`)
- Número de inimigos (`NUM_BOTS`)
- Velocidades (`PLAYER_SPEED`, `BOT_SPEED`)
- Suavidade da câmera (`CAMERA_SMOOTHNESS`)
- Vida dos personagens (`PLAYER_LIFE`, `BOT_LIFE`)
