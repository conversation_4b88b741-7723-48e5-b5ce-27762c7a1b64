# src/entities/bullet.py

import pygame
import math
from settings import BULLET_SPEED

class Bullet:
    def __init__(self, x, y, direction, bullet_type="standard"):
        """
        x, y: posição inicial (centro) da bala
        direction: tupla (dx, dy) com direção normalizada
        bullet_type: tipo da bala para efeitos visuais
        """
        self.x = float(x)
        self.y = float(y)
        self.dx = direction[0] * BULLET_SPEED
        self.dy = direction[1] * BULLET_SPEED
        self.direction = direction
        self.bullet_type = bullet_type

        # Rastro da bala para efeito visual
        self.trail = []
        self.trail_length = 6

        # Cria retângulo para colisão
        self.rect = pygame.Rect(x-2, y-2, 4, 4)

        # Propriedades visuais baseadas no tipo
        if bullet_type == "energy":
            self.color = (100, 200, 255)
            self.size = 3
            self.glow = True
        elif bullet_type == "explosive":
            self.color = (255, 150, 50)
            self.size = 4
            self.glow = False
        else:  # standard
            self.color = (255, 255, 150)
            self.size = 2
            self.glow = False

    def move(self):
        """Move a bala na direção especificada."""
        # Adiciona posição atual ao rastro
        self.trail.append((self.x, self.y))
        if len(self.trail) > self.trail_length:
            self.trail.pop(0)

        # Move a bala
        self.x += self.dx
        self.y += self.dy
        self.rect.center = (int(self.x), int(self.y))

    def draw(self, surface, camera=None):
        """Desenha a bala com efeitos visuais modernos."""
        if camera:
            screen_x = self.x - camera.x
            screen_y = self.y - camera.y

            # Só desenha se estiver visível
            if -20 <= screen_x <= 820 and -20 <= screen_y <= 620:
                self._draw_bullet(surface, screen_x, screen_y, camera)
        else:
            # Desenho direto (modo compatibilidade)
            self._draw_bullet(surface, self.x, self.y)

    def _draw_bullet(self, surface, x, y, camera=None):
        """Desenha a bala com rastro e efeitos."""
        # Desenha rastro
        for i, (trail_x, trail_y) in enumerate(self.trail):
            if camera:
                trail_screen_x = trail_x - camera.x
                trail_screen_y = trail_y - camera.y
            else:
                trail_screen_x = trail_x
                trail_screen_y = trail_y

            # Calcula transparência do rastro
            alpha_factor = i / len(self.trail) if self.trail else 0
            trail_size = max(1, int(self.size * alpha_factor))

            # Cor do rastro com transparência simulada
            trail_color = (
                int(self.color[0] * alpha_factor),
                int(self.color[1] * alpha_factor),
                int(self.color[2] * alpha_factor)
            )

            if trail_size > 0:
                pygame.draw.circle(surface, trail_color, (int(trail_screen_x), int(trail_screen_y)), trail_size)

        # Desenha bala principal
        pygame.draw.circle(surface, self.color, (int(x), int(y)), self.size)

        # Efeito de brilho para balas especiais
        if self.glow:
            glow_color = (
                min(255, self.color[0] + 50),
                min(255, self.color[1] + 50),
                min(255, self.color[2] + 50)
            )
            pygame.draw.circle(surface, glow_color, (int(x), int(y)), self.size + 1, 1)
