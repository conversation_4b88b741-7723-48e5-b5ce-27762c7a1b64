# src/entities/player.py

import pygame
import os
from settings import (PLAYER_SPEED, PLAYER_WIDTH, PLAYER_HEIGHT, MAX_BULLETS, WORLD_WIDTH, WORLD_HEIGHT,
                     PLAYER_MAX_HEALTH, PLAYER_MAX_AMMO, AMMO_PER_SHOT)
from entities.bullet import Bullet

class Player:
    def __init__(self, x, y, sprite_path, controls):
        """
        x, y: posição inicial (pixels) do topo-esquerda do jogador
        sprite_path: caminho relativo para o sprite do jogador (ex.: "assets/player1.png")
        controls: dicionário contendo as teclas de movimento e tiro:
                  {
                    'up': pygame.K_w,
                    'down': pygame.K_s,
                    'left': pygame.K_a,
                    'right': pygame.K_d,
                    'shoot': pygame.K_SPACE,
                    'bullet_dir': (1, 0)      # direção padrão dos disparos
                  }
        """
        #--- <PERSON><PERSON><PERSON> sprite, ou fallback para retângulo colorido se não existir arquivo ---#
        try:
            img = pygame.image.load(sprite_path).convert_alpha()
            self.image = pygame.transform.scale(img, (PLAYER_WIDTH, PLAYER_HEIGHT))
        except (pygame.error, FileNotFoundError):
            # Se arquivo não existe (placeholder), cria uma Surface retangular
            self.image = pygame.Surface((PLAYER_WIDTH, PLAYER_HEIGHT))
            self.image.fill((0, 0, 255))  # jogador em azul por padrão

        self.rect = self.image.get_rect(topleft=(x, y))
        self.controls = controls

        # Sistema de vida e munição melhorado
        self.max_health = PLAYER_MAX_HEALTH
        self.health = PLAYER_MAX_HEALTH
        self.max_ammo = PLAYER_MAX_AMMO
        self.current_ammo = PLAYER_MAX_AMMO

        self.bullets = []          # lista de instâncias de Bullet
        self.last_shot = 0         # timestamp (ms) do último disparo

        # Direção do movimento para tiro
        self.facing_direction = (1, 0)  # direção padrão (direita)
        self.last_movement_direction = (1, 0)  # última direção de movimento

        # Estatísticas
        self.shots_fired = 0
        self.shots_hit = 0

    def handle_movement(self, keys_pressed):
        """Move o jogador de acordo com as teclas pressionadas e atualiza direção."""
        movement_x = 0
        movement_y = 0

        if keys_pressed[self.controls['up']] and self.rect.top > 0:
            self.rect.y -= PLAYER_SPEED
            movement_y = -1
        if keys_pressed[self.controls['down']] and self.rect.bottom < WORLD_HEIGHT:
            self.rect.y += PLAYER_SPEED
            movement_y = 1
        if keys_pressed[self.controls['left']] and self.rect.left > 0:
            self.rect.x -= PLAYER_SPEED
            movement_x = -1
        if keys_pressed[self.controls['right']] and self.rect.right < WORLD_WIDTH:
            self.rect.x += PLAYER_SPEED
            movement_x = 1

        # Atualiza a direção de movimento se houve movimento
        if movement_x != 0 or movement_y != 0:
            self.last_movement_direction = (movement_x, movement_y)
            self.facing_direction = (movement_x, movement_y)

    def handle_shooting(self, keys_pressed, current_time, audio_manager=None):
        """
        Se a tecla de atirar estiver pressionada, cria uma bala na direção do movimento.
        Verifica munição disponível.
        """
        if (keys_pressed[self.controls['shoot']] and
            len(self.bullets) < MAX_BULLETS and
            current_time - self.last_shot > 300 and
            self.current_ammo > 0):  # Verifica munição

            # Cria bala no centro do jogador
            bx = self.rect.centerx
            by = self.rect.centery

            # Usa a direção do movimento atual, ou a última direção se parado
            direction = self.facing_direction

            bullet = Bullet(bx, by, direction)
            self.bullets.append(bullet)
            self.last_shot = current_time

            # Consome munição
            self.current_ammo -= AMMO_PER_SHOT
            self.shots_fired += 1

            # Toca o som do tiro
            if audio_manager:
                audio_manager.play_shoot_sound()

    def update_bullets(self):
        """
        Atualiza todas as balas: move e remove aquelas que saíram do mundo.
        """
        for bullet in self.bullets[:]:
            bullet.move()
            # Se a bala estiver fora do mundo, remove
            world_rect = pygame.Rect(0, 0, WORLD_WIDTH, WORLD_HEIGHT)
            if not world_rect.colliderect(bullet.rect):
                self.bullets.remove(bullet)

    def draw(self, surface, camera=None):
        """Desenha o sprite do jogador e todas as balas na tela."""
        if camera:
            # Só desenha se estiver visível na câmera
            if camera.is_visible(self.rect):
                screen_rect = camera.apply(self.rect)
                surface.blit(self.image, screen_rect)
            # Desenha balas visíveis
            for bullet in self.bullets:
                if camera.is_visible(bullet.rect):
                    bullet.draw(surface, camera)
        else:
            # Modo compatibilidade (sem câmera)
            surface.blit(self.image, self.rect)
            for bullet in self.bullets:
                bullet.draw(surface)

    def take_damage(self, damage):
        """
        Recebe dano e atualiza a vida.
        """
        self.health = max(0, self.health - damage)
        return self.health <= 0  # Retorna True se morreu

    def is_alive(self):
        """
        Verifica se o jogador está vivo.
        """
        return self.health > 0

    def get_accuracy(self):
        """
        Calcula a precisão do jogador.
        """
        if self.shots_fired == 0:
            return 0
        return int((self.shots_hit / self.shots_fired) * 100)

    def reload(self):
        """
        Recarrega a munição (para futuras implementações).
        """
        self.current_ammo = self.max_ammo
