# src/entities/bot.py

import pygame
import random
import math
from settings import (BOT_SPEED, BOT_MAX_BULLETS, WORLD_WIDTH, WORLD_HEIGHT,
                     BOT_DETECTION_RANGE, BOT_ATTACK_RANGE, BOT_PATROL_RADIUS,
                     BOT_SHOOT_COOLDOWN, BOT_ACCURACY, BOT_PREDICTION_FACTOR,
                     ELITE_BOT_CHANCE, SWARM_BEHAVIOR, BOT_MAX_HEALTH)
from entities.player import Player
from entities.bullet import Bullet

class Bot(Player):
    def __init__(self, x, y, sprite_path, target, bot_list=None):
        """
        x, y: posição inicial do Bot
        sprite_path: caminho do sprite
        target: instância de Player que o Bot vai perseguir/atacar
        bot_list: lista de outros bots para coordenação
        """
        super().__init__(x, y, sprite_path, controls={})
        self.target = target
        self.bot_list = bot_list or []

        # Determina se é um bot elite
        self.is_elite = random.random() < ELITE_BOT_CHANCE

        # Configurações baseadas no tipo
        if self.is_elite:
            self.max_health = BOT_MAX_HEALTH + 50  # Bots elite têm mais vida
            self.health = self.max_health
            self.speed_multiplier = 1.3
            self.accuracy_bonus = 0.1
            self.detection_bonus = 50
            # Cor diferente para bots elite (vermelho mais escuro)
            if hasattr(self, 'image'):
                self.image.fill((150, 0, 0))
        else:
            self.max_health = BOT_MAX_HEALTH
            self.health = BOT_MAX_HEALTH
            self.speed_multiplier = 1.0
            self.accuracy_bonus = 0.0
            self.detection_bonus = 0

        # IA super avançada
        self.state = "patrol"  # patrol, chase, attack, flank, retreat
        self.patrol_target_x = x
        self.patrol_target_y = y
        self.detection_range = BOT_DETECTION_RANGE + self.detection_bonus
        self.attack_range = BOT_ATTACK_RANGE
        self.patrol_radius = BOT_PATROL_RADIUS
        self.direction_change_timer = 0
        self.wander_direction = random.choice([(1, 0), (-1, 0), (0, 1), (0, -1)])

        # Sistemas avançados de IA
        self.player_last_seen_pos = None
        self.player_velocity = (0, 0)
        self.last_player_pos = (target.rect.centerx, target.rect.centery)
        self.prediction_accuracy = BOT_ACCURACY + self.accuracy_bonus
        self.dodge_timer = 0
        self.dodge_direction = (0, 0)
        self.group_coordination_timer = 0
        self.preferred_distance = random.randint(60, 120)  # Distância preferida do alvo
        self.aggression_level = random.uniform(0.7, 1.0)  # Nível de agressividade
        self.retreat_health_threshold = 1  # Recua quando vida baixa

    def get_distance_to_target(self):
        """Calcula a distância até o alvo."""
        dx = self.target.rect.centerx - self.rect.centerx
        dy = self.target.rect.centery - self.rect.centery
        return math.sqrt(dx * dx + dy * dy)

    def update_player_tracking(self):
        """Atualiza o rastreamento do jogador para predição."""
        current_pos = (self.target.rect.centerx, self.target.rect.centery)

        # Calcula velocidade do jogador
        if self.last_player_pos:
            self.player_velocity = (
                current_pos[0] - self.last_player_pos[0],
                current_pos[1] - self.last_player_pos[1]
            )

        self.last_player_pos = current_pos

        # Atualiza última posição vista
        if self.get_distance_to_target() <= self.detection_range:
            self.player_last_seen_pos = current_pos

    def predict_player_position(self, time_ahead=0.5):
        """Prediz onde o jogador estará baseado na velocidade atual."""
        if not self.player_last_seen_pos:
            return self.target.rect.centerx, self.target.rect.centery

        # Predição baseada na velocidade
        predicted_x = self.player_last_seen_pos[0] + (self.player_velocity[0] * time_ahead * BOT_PREDICTION_FACTOR)
        predicted_y = self.player_last_seen_pos[1] + (self.player_velocity[1] * time_ahead * BOT_PREDICTION_FACTOR)

        return predicted_x, predicted_y

    def get_nearby_bots(self, radius=200):
        """Retorna bots próximos para coordenação."""
        nearby = []
        for bot in self.bot_list:
            if bot != self and bot.life > 0:
                dx = bot.rect.centerx - self.rect.centerx
                dy = bot.rect.centery - self.rect.centery
                distance = math.sqrt(dx * dx + dy * dy)
                if distance <= radius:
                    nearby.append(bot)
        return nearby

    def ai_move(self, current_time):
        """IA super avançada para movimento."""
        self.update_player_tracking()
        distance_to_target = self.get_distance_to_target()

        # Determina estado baseado em múltiplos fatores
        if self.health <= self.retreat_health_threshold * self.max_health:
            self.state = "retreat"
        elif distance_to_target <= self.attack_range * self.aggression_level:
            self.state = "attack"
        elif distance_to_target <= self.detection_range:
            # Decide entre chase e flank baseado na situação
            nearby_bots = self.get_nearby_bots()
            if len(nearby_bots) >= 2 and SWARM_BEHAVIOR:
                self.state = "flank"
            else:
                self.state = "chase"
        else:
            self.state = "patrol"

        # Executa comportamento baseado no estado
        if self.state == "patrol":
            self._patrol_behavior(current_time)
        elif self.state == "chase":
            self._chase_behavior()
        elif self.state == "attack":
            self._attack_behavior(current_time)
        elif self.state == "flank":
            self._flank_behavior(current_time)
        elif self.state == "retreat":
            self._retreat_behavior()

        # Sistema de esquiva de balas
        self._dodge_bullets(current_time)

        # Limita movimento aos limites do mundo
        self.rect.x = max(0, min(self.rect.x, WORLD_WIDTH - self.rect.width))
        self.rect.y = max(0, min(self.rect.y, WORLD_HEIGHT - self.rect.height))

    def _patrol_behavior(self, current_time):
        """Comportamento de patrulha inteligente."""
        if current_time - self.direction_change_timer > random.randint(1500, 3000):
            self.wander_direction = random.choice([(1, 0), (-1, 0), (0, 1), (0, -1), (1, 1), (-1, -1), (1, -1), (-1, 1)])
            self.direction_change_timer = current_time

        speed = BOT_SPEED * self.speed_multiplier
        new_x = self.rect.x + self.wander_direction[0] * speed
        new_y = self.rect.y + self.wander_direction[1] * speed

        patrol_distance = math.sqrt((new_x - self.patrol_target_x)**2 + (new_y - self.patrol_target_y)**2)
        if patrol_distance < self.patrol_radius:
            self.rect.x = new_x
            self.rect.y = new_y

    def _chase_behavior(self):
        """Perseguição inteligente com predição."""
        predicted_x, predicted_y = self.predict_player_position()

        dx = predicted_x - self.rect.centerx
        dy = predicted_y - self.rect.centery
        distance = math.sqrt(dx * dx + dy * dy)

        if distance > 0:
            speed = BOT_SPEED * self.speed_multiplier
            self.rect.x += int((dx / distance) * speed)
            self.rect.y += int((dy / distance) * speed)

    def _attack_behavior(self, current_time):
        """Comportamento de ataque avançado com movimento tático."""
        dx = self.target.rect.centerx - self.rect.centerx
        dy = self.target.rect.centery - self.rect.centery
        distance = math.sqrt(dx * dx + dy * dy)

        if distance > 0:
            # Movimento tático: mantém distância preferida
            if distance > self.preferred_distance + 20:
                # Muito longe, aproxima-se
                speed = BOT_SPEED * self.speed_multiplier
                self.rect.x += int((dx / distance) * speed)
                self.rect.y += int((dy / distance) * speed)
            elif distance < self.preferred_distance - 20:
                # Muito perto, afasta-se
                speed = BOT_SPEED * self.speed_multiplier
                self.rect.x -= int((dx / distance) * speed)
                self.rect.y -= int((dy / distance) * speed)
            else:
                # Distância ideal, movimento circular
                angle = math.atan2(dy, dx) + (0.15 if self.is_elite else 0.1)
                target_x = self.target.rect.centerx + math.cos(angle) * self.preferred_distance
                target_y = self.target.rect.centery + math.sin(angle) * self.preferred_distance

                move_dx = target_x - self.rect.centerx
                move_dy = target_y - self.rect.centery
                move_distance = math.sqrt(move_dx * move_dx + move_dy * move_dy)

                if move_distance > 0:
                    speed = BOT_SPEED * self.speed_multiplier
                    self.rect.x += int((move_dx / move_distance) * speed)
                    self.rect.y += int((move_dy / move_distance) * speed)

    def _flank_behavior(self, current_time):
        """Comportamento de flanqueamento coordenado."""
        nearby_bots = self.get_nearby_bots()

        if len(nearby_bots) >= 1:
            # Calcula posição de flanqueamento
            player_x, player_y = self.target.rect.centerx, self.target.rect.centery

            # Cada bot tenta uma posição diferente ao redor do jogador
            bot_index = hash(id(self)) % 4  # 0-3 baseado no ID do bot
            angles = [0, math.pi/2, math.pi, 3*math.pi/2]  # 4 posições cardinais
            angle = angles[bot_index] + (current_time * 0.001)  # Rotação lenta

            flank_distance = 100
            target_x = player_x + math.cos(angle) * flank_distance
            target_y = player_y + math.sin(angle) * flank_distance

            # Move em direção à posição de flanqueamento
            dx = target_x - self.rect.centerx
            dy = target_y - self.rect.centery
            distance = math.sqrt(dx * dx + dy * dy)

            if distance > 0:
                speed = BOT_SPEED * self.speed_multiplier * 1.2  # Mais rápido no flank
                self.rect.x += int((dx / distance) * speed)
                self.rect.y += int((dy / distance) * speed)
        else:
            # Fallback para chase se não há bots próximos
            self._chase_behavior()

    def _retreat_behavior(self):
        """Comportamento de recuo quando com pouca vida."""
        dx = self.target.rect.centerx - self.rect.centerx
        dy = self.target.rect.centery - self.rect.centery
        distance = math.sqrt(dx * dx + dy * dy)

        if distance > 0:
            # Move na direção oposta ao jogador
            speed = BOT_SPEED * self.speed_multiplier * 1.5  # Mais rápido na fuga
            self.rect.x -= int((dx / distance) * speed)
            self.rect.y -= int((dy / distance) * speed)

    def _dodge_bullets(self, current_time):
        """Sistema de esquiva de balas do jogador."""
        if current_time - self.dodge_timer > 200:  # Verifica a cada 200ms
            # Verifica se há balas próximas
            for bullet in self.target.bullets:
                bullet_dx = bullet.rect.centerx - self.rect.centerx
                bullet_dy = bullet.rect.centery - self.rect.centery
                bullet_distance = math.sqrt(bullet_dx * bullet_dx + bullet_dy * bullet_dy)

                # Se bala está próxima e vindo na nossa direção
                if bullet_distance < 80:
                    # Calcula direção perpendicular à bala para esquiva
                    if abs(bullet.dx) > abs(bullet.dy):
                        # Bala horizontal, esquiva vertical
                        self.dodge_direction = (0, 1 if random.random() > 0.5 else -1)
                    else:
                        # Bala vertical, esquiva horizontal
                        self.dodge_direction = (1 if random.random() > 0.5 else -1, 0)

                    self.dodge_timer = current_time
                    break

        # Aplica movimento de esquiva
        if current_time - self.dodge_timer < 300:  # Esquiva por 300ms
            dodge_speed = BOT_SPEED * 2
            self.rect.x += self.dodge_direction[0] * dodge_speed
            self.rect.y += self.dodge_direction[1] * dodge_speed

    def ai_shoot(self, current_time):
        """Sistema de tiro super avançado com predição e precisão."""
        if self.state not in ["attack", "flank"]:
            return

        distance_to_target = self.get_distance_to_target()

        # Só atira se estiver no alcance
        if distance_to_target <= self.attack_range and distance_to_target > 0:
            # Prediz posição futura do jogador para tiro mais preciso
            predicted_x, predicted_y = self.predict_player_position(0.3)

            dx = predicted_x - self.rect.centerx
            dy = predicted_y - self.rect.centery
            distance = math.sqrt(dx * dx + dy * dy)

            if distance > 0:
                # Aplica precisão baseada no tipo de bot
                accuracy = self.prediction_accuracy

                # Adiciona pequeno erro aleatório baseado na precisão
                error_factor = 1.0 - accuracy
                angle_error = (random.random() - 0.5) * error_factor * 0.5

                # Calcula direção com erro
                angle = math.atan2(dy, dx) + angle_error
                direction = (math.cos(angle), math.sin(angle))

                # Cooldown baseado no tipo de bot
                cooldown = BOT_SHOOT_COOLDOWN if not self.is_elite else BOT_SHOOT_COOLDOWN * 0.7

                if len(self.bullets) < BOT_MAX_BULLETS and current_time - self.last_shot > cooldown:
                    bx = self.rect.centerx
                    by = self.rect.centery
                    bullet = Bullet(bx, by, direction)
                    self.bullets.append(bullet)
                    self.last_shot = current_time

    def take_damage(self, damage=25):
        """Recebe dano e pode alterar comportamento."""
        self.health -= damage
        self.health = max(0, self.health)

        # Aumenta agressividade quando ferido
        if self.health <= self.retreat_health_threshold * self.max_health:
            self.aggression_level = min(1.0, self.aggression_level + 0.2)

        return self.health <= 0  # Retorna True se morreu

    def update(self, current_time):
        """
        Atualização principal do bot com IA avançada.
        """
        self.ai_move(current_time)
        self.ai_shoot(current_time)
        self.update_bullets()
