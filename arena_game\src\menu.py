# src/menu.py

import pygame
import sys
from settings import WIDTH, HEIGHT, WHITE, BLACK, UI_BLUE, UI_GREEN, UI_RED, UI_DARK_GRAY
from ui import ui

class GameState:
    MENU = "menu"
    PLAYING = "playing"
    GAME_OVER = "game_over"
    PAUSED = "paused"

class Menu:
    def __init__(self):
        """
        Inicializa o sistema de menus do jogo.
        """
        self.state = GameState.MENU
        self.player_name = ""
        self.input_active = False
        self.blink_timer = 0
        
    def handle_menu_events(self, events):
        """
        Manipula eventos do menu principal.
        """
        for event in events:
            if event.type == pygame.KEYDOWN:
                if self.input_active:
                    if event.key == pygame.K_RETURN:
                        if len(self.player_name.strip()) > 0:
                            return GameState.PLAYING
                    elif event.key == pygame.K_BACKSPACE:
                        self.player_name = self.player_name[:-1]
                    elif event.unicode.isprintable() and len(self.player_name) < 15:
                        self.player_name += event.unicode
                else:
                    if event.key == pygame.K_RETURN:
                        self.input_active = True
                    elif event.key == pygame.K_ESCAPE:
                        return "quit"
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                mouse_pos = pygame.mouse.get_pos()
                
                # Verifica clique na caixa de input
                input_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2, 300, 40)
                if input_rect.collidepoint(mouse_pos):
                    self.input_active = True
                
                # Verifica clique no botão jogar
                if len(self.player_name.strip()) > 0:
                    play_rect = pygame.Rect(WIDTH//2 - 100, HEIGHT//2 + 80, 200, 50)
                    if play_rect.collidepoint(mouse_pos):
                        return GameState.PLAYING
                
                # Verifica clique no botão sair
                quit_rect = pygame.Rect(WIDTH//2 - 100, HEIGHT//2 + 140, 200, 50)
                if quit_rect.collidepoint(mouse_pos):
                    return "quit"
        
        return GameState.MENU
    
    def handle_game_over_events(self, events, stats):
        """
        Manipula eventos da tela de game over.
        """
        for event in events:
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r:
                    return "restart"
                elif event.key == pygame.K_q or event.key == pygame.K_ESCAPE:
                    return "quit"
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                mouse_pos = pygame.mouse.get_pos()
                
                # Botão reiniciar
                restart_rect = pygame.Rect(WIDTH//2 - 120, HEIGHT//2 + 100, 100, 50)
                if restart_rect.collidepoint(mouse_pos):
                    return "restart"
                
                # Botão sair
                quit_rect = pygame.Rect(WIDTH//2 + 20, HEIGHT//2 + 100, 100, 50)
                if quit_rect.collidepoint(mouse_pos):
                    return "quit"
        
        return GameState.GAME_OVER
    
    def draw_menu(self, surface):
        """
        Desenha o menu principal.
        """
        # Fundo
        surface.fill(UI_DARK_GRAY)
        
        # Título
        ui.draw_text(surface, "ARENA BATTLE ROYALE", ui.font_title, WHITE, 
                    WIDTH//2, HEIGHT//2 - 150, center=True)
        
        # Subtítulo
        ui.draw_text(surface, "Sobreviva aos inimigos inteligentes!", ui.font_large, UI_BLUE, 
                    WIDTH//2, HEIGHT//2 - 100, center=True)
        
        # Label do input
        ui.draw_text(surface, "Digite seu nickname:", ui.font_normal, WHITE, 
                    WIDTH//2, HEIGHT//2 - 40, center=True)
        
        # Caixa de input
        ui.draw_input_box(surface, self.player_name, WIDTH//2 - 150, HEIGHT//2, 300, 40, self.input_active)
        
        # Botão jogar (só aparece se há nome)
        if len(self.player_name.strip()) > 0:
            ui.draw_button(surface, "JOGAR", WIDTH//2 - 100, HEIGHT//2 + 80, 200, 50, 
                          UI_GREEN, WHITE)
        else:
            ui.draw_button(surface, "Digite um nome", WIDTH//2 - 100, HEIGHT//2 + 80, 200, 50, 
                          UI_DARK_GRAY, UI_DARK_GRAY)
        
        # Botão sair
        ui.draw_button(surface, "SAIR", WIDTH//2 - 100, HEIGHT//2 + 140, 200, 50, 
                      UI_RED, WHITE)
        
        # Instruções
        instructions = [
            "CONTROLES:",
            "WASD - Movimento",
            "ESPAÇO - Atirar",
            "M - Ligar/Desligar Som",
            "ESC - Pausar"
        ]
        
        for i, instruction in enumerate(instructions):
            color = WHITE if i == 0 else UI_BLUE
            font = ui.font_normal if i == 0 else ui.font_small
            ui.draw_text(surface, instruction, font, color, 50, HEIGHT - 150 + i * 25)
        
        # Informações do jogo
        info = [
            "• 8 inimigos inteligentes",
            "• Bots elite mais perigosos",
            "• Mundo grande para explorar",
            "• Sistema de vida e munição realista"
        ]
        
        for i, info_text in enumerate(info):
            ui.draw_text(surface, info_text, ui.font_small, UI_BLUE, 
                        WIDTH - 350, HEIGHT - 120 + i * 25)
    
    def draw_game_over(self, surface, stats):
        """
        Desenha a tela de game over.
        """
        # Fundo semi-transparente
        overlay = pygame.Surface((WIDTH, HEIGHT))
        overlay.set_alpha(180)
        overlay.fill(BLACK)
        surface.blit(overlay, (0, 0))
        
        # Painel central
        panel_rect = pygame.Rect(WIDTH//2 - 200, HEIGHT//2 - 150, 400, 300)
        pygame.draw.rect(surface, UI_DARK_GRAY, panel_rect)
        pygame.draw.rect(surface, WHITE, panel_rect, 3)
        
        # Título
        title = "VITÓRIA!" if stats['victory'] else "GAME OVER"
        title_color = UI_GREEN if stats['victory'] else UI_RED
        ui.draw_text(surface, title, ui.font_title, title_color, 
                    WIDTH//2, HEIGHT//2 - 120, center=True)
        
        # Nome do jogador
        ui.draw_text(surface, f"Jogador: {self.player_name}", ui.font_large, WHITE, 
                    WIDTH//2, HEIGHT//2 - 80, center=True)
        
        # Estatísticas
        stats_text = [
            f"Inimigos eliminados: {stats['bots_killed']}/{stats['total_bots']}",
            f"Tempo de jogo: {stats['time_played']}",
            f"Precisão: {stats['accuracy']}%",
            f"Vida restante: {stats['health_remaining']}%"
        ]
        
        for i, stat in enumerate(stats_text):
            ui.draw_text(surface, stat, ui.font_normal, WHITE, 
                        WIDTH//2, HEIGHT//2 - 40 + i * 25, center=True)
        
        # Botões
        ui.draw_button(surface, "REINICIAR (R)", WIDTH//2 - 120, HEIGHT//2 + 100, 100, 50, 
                      UI_GREEN, WHITE)
        ui.draw_button(surface, "SAIR (Q)", WIDTH//2 + 20, HEIGHT//2 + 100, 100, 50, 
                      UI_RED, WHITE)
        
        # Instruções
        ui.draw_text(surface, "Pressione R para jogar novamente ou Q para sair", 
                    ui.font_small, UI_BLUE, WIDTH//2, HEIGHT//2 + 170, center=True)
    
    def draw_pause_menu(self, surface):
        """
        Desenha o menu de pausa.
        """
        # Fundo semi-transparente
        overlay = pygame.Surface((WIDTH, HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        surface.blit(overlay, (0, 0))
        
        # Painel central
        panel_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 - 100, 300, 200)
        pygame.draw.rect(surface, UI_DARK_GRAY, panel_rect)
        pygame.draw.rect(surface, WHITE, panel_rect, 3)
        
        # Título
        ui.draw_text(surface, "JOGO PAUSADO", ui.font_large, WHITE, 
                    WIDTH//2, HEIGHT//2 - 60, center=True)
        
        # Instruções
        instructions = [
            "ESC - Continuar",
            "R - Reiniciar",
            "Q - Sair para menu"
        ]
        
        for i, instruction in enumerate(instructions):
            ui.draw_text(surface, instruction, ui.font_normal, UI_BLUE, 
                        WIDTH//2, HEIGHT//2 - 20 + i * 30, center=True)

# Instância global do menu
menu = Menu()
