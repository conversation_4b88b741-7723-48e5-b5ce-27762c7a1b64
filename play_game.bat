@echo off
title Arena Battle Royale - Tactical Combat Edition

echo ================================================================
echo     🎮 ARENA BATTLE ROYALE - TACTICAL COMBAT EDITION 🎮
echo ================================================================
echo.
echo 🎯 MIRA 360°: Use o mouse para mirar em qualquer direção
echo ⚡ SKILLS ÚNICAS: Pressione B, N, M para habilidades especiais
echo 🌐 MULTIPLAYER: Jogue com amigos na rede local
echo 🎨 GRÁFICOS MODERNOS: Visual futurista e tático
echo 🏗️ OBJETOS 3D: 18 obstáculos táticos para cobertura
echo.
echo 📋 CONTROLES:
echo    WASD - Movimento
echo    Mouse - Mira 360°
echo    Clique Esquerdo/Espaço - Atirar
echo    B, N, M - Skills do personagem
echo    ESC - Pausar
echo.
echo 🚀 Iniciando jogo...
echo --------------------------------------------------
echo.

REM Verifica se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERRO: Python não encontrado!
    echo    Instale Python em: https://python.org
    pause
    exit /b 1
)

REM Verifica se o arquivo do jogo existe
if not exist "arena_game\src\main.py" (
    echo ❌ ERRO: Arquivos do jogo não encontrados!
    echo    Procurando: arena_game\src\main.py
    echo    Verifique se a pasta arena_game\src\ existe
    pause
    exit /b 1
)

REM Executa o jogo
python play_game.py

REM Pausa para ver mensagens de erro se houver
if errorlevel 1 (
    echo.
    echo ❌ O jogo encerrou com erro
    pause
)
