#!/usr/bin/env python3
# play_game.py - Launcher principal do Arena Battle Royale

"""
🎮 ARENA BATTLE ROYALE - TACTICAL COMBAT EDITION 🎮

Launcher principal para iniciar o jogo do diretório raiz.
Execute: python play_game.py
"""

import sys
import os

def main():
    print("=" * 70)
    print("    🎮 ARENA BATTLE ROYALE - TACTICAL COMBAT EDITION 🎮")
    print("=" * 70)
    print()
    print("🎯 MIRA 360°: Use o mouse para mirar em qualquer direção")
    print("⚡ SKILLS ÚNICAS: Pressione B, N, M para habilidades especiais")
    print("🌐 MULTIPLAYER: Jogue com amigos na rede local")
    print("🎨 GRÁFICOS MODERNOS: Visual futurista e tático")
    print("🏗️ OBJETOS 3D: 18 obstáculos táticos para cobertura")
    print()
    print("📋 CONTROLES:")
    print("   WASD - Movimento")
    print("   Mouse - Mira 360°")
    print("   Clique Esquerdo/Espaço - Atirar")
    print("   B, N, M - Skills do personagem")
    print("   ESC - Pausar")
    print()
    print("🚀 Iniciando jogo...")
    print("-" * 50)
    
    # Verifica se a estrutura do jogo existe
    game_dir = os.path.join(os.path.dirname(__file__), 'arena_game', 'src')
    main_file = os.path.join(game_dir, 'main.py')
    
    if not os.path.exists(main_file):
        print("❌ ERRO: Arquivos do jogo não encontrados!")
        print(f"   Procurando em: {main_file}")
        print("   Verifique se a pasta arena_game/src/ existe")
        return
    
    # Adiciona diretório src ao path
    sys.path.insert(0, game_dir)
    
    # Muda para diretório do jogo
    original_dir = os.getcwd()
    os.chdir(game_dir)
    
    try:
        # Importa e executa o jogo
        from main import main as game_main
        game_main()
    except ImportError as e:
        print(f"❌ ERRO: Não foi possível importar módulos do jogo")
        print(f"   Detalhes: {e}")
        print("   Solução: pip install pygame")
    except Exception as e:
        print(f"❌ ERRO: Falha ao executar o jogo")
        print(f"   Detalhes: {e}")
    finally:
        # Restaura diretório original
        os.chdir(original_dir)

if __name__ == "__main__":
    main()
