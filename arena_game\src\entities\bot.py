# src/entities/bot.py

import pygame
import random
import math
from settings import BOT_SPEED, MAX_BULLETS, BOT_LIFE, WORLD_WIDTH, WORLD_HEIGHT
from entities.player import Player
from entities.bullet import Bullet

class Bot(Player):
    def __init__(self, x, y, sprite_path, target):
        """
        x, y: posição inicial do Bot
        sprite_path: caminho do sprite (pode usar o mesmo do player)
        target: instância de Player que o Bot vai perseguir/atacar
        """
        # Chamamos __init__ de Player, mas não definimos controles (não usados)
        super().__init__(x, y, sprite_path, controls={})
        self.target = target
        self.life = BOT_LIFE

        # IA melhorada
        self.state = "patrol"  # patrol, chase, attack
        self.patrol_target_x = x
        self.patrol_target_y = y
        self.detection_range = 200
        self.attack_range = 150
        self.patrol_radius = 100
        self.direction_change_timer = 0
        self.wander_direction = random.choice([(1, 0), (-1, 0), (0, 1), (0, -1)])

    def get_distance_to_target(self):
        """Calcula a distância até o alvo."""
        dx = self.target.rect.centerx - self.rect.centerx
        dy = self.target.rect.centery - self.rect.centery
        return math.sqrt(dx * dx + dy * dy)

    def ai_move(self, current_time):
        """IA melhorada para movimento."""
        distance_to_target = self.get_distance_to_target()

        # Determina o estado baseado na distância
        if distance_to_target <= self.attack_range:
            self.state = "attack"
        elif distance_to_target <= self.detection_range:
            self.state = "chase"
        else:
            self.state = "patrol"

        if self.state == "patrol":
            self._patrol_behavior(current_time)
        elif self.state == "chase":
            self._chase_behavior()
        elif self.state == "attack":
            self._attack_behavior()

        # Limita movimento aos limites do mundo
        self.rect.x = max(0, min(self.rect.x, WORLD_WIDTH - self.rect.width))
        self.rect.y = max(0, min(self.rect.y, WORLD_HEIGHT - self.rect.height))

    def _patrol_behavior(self, current_time):
        """Comportamento de patrulha - movimento aleatório."""
        # Muda direção ocasionalmente
        if current_time - self.direction_change_timer > 2000:  # 2 segundos
            self.wander_direction = random.choice([(1, 0), (-1, 0), (0, 1), (0, -1), (1, 1), (-1, -1), (1, -1), (-1, 1)])
            self.direction_change_timer = current_time

        # Move na direção escolhida
        new_x = self.rect.x + self.wander_direction[0] * BOT_SPEED
        new_y = self.rect.y + self.wander_direction[1] * BOT_SPEED

        # Verifica se não saiu muito longe do ponto de patrulha
        patrol_distance = math.sqrt((new_x - self.patrol_target_x)**2 + (new_y - self.patrol_target_y)**2)
        if patrol_distance < self.patrol_radius:
            self.rect.x = new_x
            self.rect.y = new_y

    def _chase_behavior(self):
        """Comportamento de perseguição - move em direção ao alvo."""
        dx = self.target.rect.centerx - self.rect.centerx
        dy = self.target.rect.centery - self.rect.centery
        distance = math.sqrt(dx * dx + dy * dy)

        if distance > 0:
            # Normaliza a direção e aplica velocidade
            self.rect.x += int((dx / distance) * BOT_SPEED)
            self.rect.y += int((dy / distance) * BOT_SPEED)

    def _attack_behavior(self):
        """Comportamento de ataque - mantém distância e circula o alvo."""
        dx = self.target.rect.centerx - self.rect.centerx
        dy = self.target.rect.centery - self.rect.centery
        distance = math.sqrt(dx * dx + dy * dy)

        if distance > 0:
            # Movimento circular ao redor do alvo
            angle = math.atan2(dy, dx) + 0.1  # Adiciona rotação
            target_distance = 80  # Distância desejada do alvo

            target_x = self.target.rect.centerx + math.cos(angle) * target_distance
            target_y = self.target.rect.centery + math.sin(angle) * target_distance

            # Move em direção à posição circular
            move_dx = target_x - self.rect.centerx
            move_dy = target_y - self.rect.centery
            move_distance = math.sqrt(move_dx * move_dx + move_dy * move_dy)

            if move_distance > 0:
                self.rect.x += int((move_dx / move_distance) * BOT_SPEED)
                self.rect.y += int((move_dy / move_distance) * BOT_SPEED)

    def ai_shoot(self, current_time):
        """IA melhorada para tiro."""
        if self.state != "attack":
            return

        dx = self.target.rect.centerx - self.rect.centerx
        dy = self.target.rect.centery - self.rect.centery
        distance = math.sqrt(dx * dx + dy * dy)

        # Atira se estiver no alcance e com linha de visão
        if distance <= self.attack_range and distance > 0:
            # Calcula direção normalizada
            direction = (dx / distance, dy / distance)

            if len(self.bullets) < MAX_BULLETS and current_time - self.last_shot > 800:
                bx = self.rect.centerx
                by = self.rect.centery
                bullet = Bullet(bx, by, direction)
                self.bullets.append(bullet)
                self.last_shot = current_time

    def update(self, current_time):
        """
        Chame este método no loop principal, em vez de handle_movement/handle_shooting.
        """
        self.ai_move(current_time)
        self.ai_shoot(current_time)
        self.update_bullets()
