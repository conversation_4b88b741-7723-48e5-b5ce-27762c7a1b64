# src/world.py

import pygame
from settings import WORLD_WIDTH, WORLD_HEIGHT, WHITE, GRAY, BLACK

class World:
    def __init__(self):
        """
        Inicializa o mundo do jogo com um fundo e elementos visuais.
        """
        self.width = WORLD_WIDTH
        self.height = WORLD_HEIGHT
        
        # Cria uma superfície para o fundo do mundo
        self.background = pygame.Surface((WORLD_WIDTH, WORLD_HEIGHT))
        self._create_background()
        
    def _create_background(self):
        """Cria o fundo do mundo com um padrão de grade."""
        # Preenche com cor base
        self.background.fill(WHITE)
        
        # Desenha uma grade
        grid_size = 50
        for x in range(0, WORLD_WIDTH, grid_size):
            pygame.draw.line(self.background, GRAY, (x, 0), (x, WORLD_HEIGHT), 1)
        for y in range(0, WORLD_HEIGHT, grid_size):
            pygame.draw.line(self.background, GRAY, (0, y), (WORLD_WIDTH, y), 1)
            
        # Desenha bordas do mundo
        pygame.draw.rect(self.background, BLACK, (0, 0, WORLD_WIDTH, WORLD_HEIGHT), 5)
        
        # Adiciona alguns elementos decorativos
        self._add_decorative_elements()
        
    def _add_decorative_elements(self):
        """Adiciona elementos decorativos ao mundo."""
        # Adiciona alguns retângulos como obstáculos visuais
        obstacles = [
            (300, 200, 100, 100),
            (800, 400, 150, 80),
            (1500, 300, 120, 120),
            (2000, 800, 200, 100),
            (500, 1200, 180, 90),
            (1200, 1000, 100, 150),
            (1800, 1400, 160, 120),
        ]
        
        for x, y, w, h in obstacles:
            pygame.draw.rect(self.background, GRAY, (x, y, w, h))
            pygame.draw.rect(self.background, BLACK, (x, y, w, h), 2)
    
    def draw(self, surface, camera):
        """
        Desenha a parte visível do mundo na tela.
        """
        # Calcula qual parte do fundo deve ser desenhada
        camera_rect = pygame.Rect(camera.x, camera.y, surface.get_width(), surface.get_height())
        
        # Desenha a parte visível do fundo
        surface.blit(self.background, (0, 0), camera_rect)
