# src/multiplayer_menu.py

import pygame
import math
import time
from settings import WIDTH, HEIGHT, WHITE, BLACK, UI_BLUE, UI_GREEN, UI_RED, UI_DARK_GRAY
from ui import ui
from network_client import network_client, ServerDiscovery

class MultiplayerMenu:
    def __init__(self):
        """
        Menu para escolher modo de jogo e conectar a servidores.
        """
        self.state = "mode_select"  # mode_select, server_list, connect, hosting
        self.selected_option = 0
        self.server_ip = "127.0.0.1"
        self.server_port = "12345"
        self.input_active = False
        self.input_field = "ip"  # ip, port
        self.blink_timer = 0

        # Lista de servidores descobertos
        self.discovered_servers = []
        self.scanning = False

    def handle_events(self, events):
        """Gerencia eventos do menu multiplayer."""
        for event in events:
            if event.type == pygame.KEYDOWN:
                if self.state == "mode_select":
                    return self._handle_mode_select_keys(event)
                elif self.state == "server_list":
                    return self._handle_server_list_keys(event)
                elif self.state == "connect":
                    return self._handle_connect_keys(event)

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if self.state == "mode_select":
                    return self._handle_mode_select_mouse(event)
                elif self.state == "server_list":
                    return self._handle_server_list_mouse(event)
                elif self.state == "connect":
                    return self._handle_connect_mouse(event)

        return None

    def _handle_mode_select_keys(self, event):
        """Gerencia teclas na seleção de modo."""
        if event.key == pygame.K_UP:
            self.selected_option = (self.selected_option - 1) % 3
        elif event.key == pygame.K_DOWN:
            self.selected_option = (self.selected_option + 1) % 3
        elif event.key == pygame.K_RETURN:
            if self.selected_option == 0:  # Single Player
                return "singleplayer"
            elif self.selected_option == 1:  # Multiplayer
                self.state = "server_list"
                self._scan_for_servers()
            elif self.selected_option == 2:  # Hospedar
                return "server_config"
        elif event.key == pygame.K_ESCAPE:
            return "back"

        return None

    def _handle_mode_select_mouse(self, event):
        """Gerencia cliques na seleção de modo."""
        mouse_pos = pygame.mouse.get_pos()

        # Botões do modo de jogo
        single_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 - 60, 300, 50)
        multi_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2, 300, 50)
        host_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 + 60, 300, 50)
        back_rect = pygame.Rect(50, HEIGHT - 80, 100, 50)

        if single_rect.collidepoint(mouse_pos):
            return "singleplayer"
        elif multi_rect.collidepoint(mouse_pos):
            self.state = "server_list"
            self._scan_for_servers()
        elif host_rect.collidepoint(mouse_pos):
            return "server_config"
        elif back_rect.collidepoint(mouse_pos):
            return "back"

        return None

    def _handle_server_list_keys(self, event):
        """Gerencia teclas na lista de servidores."""
        if event.key == pygame.K_ESCAPE:
            self.state = "mode_select"
        elif event.key == pygame.K_F5:
            self._scan_for_servers()
        elif event.key == pygame.K_c:
            self.state = "connect"

        return None

    def _handle_server_list_mouse(self, event):
        """Gerencia cliques na lista de servidores."""
        mouse_pos = pygame.mouse.get_pos()

        # Botões
        back_rect = pygame.Rect(50, HEIGHT - 80, 100, 50)
        refresh_rect = pygame.Rect(WIDTH//2 - 100, HEIGHT - 80, 100, 50)
        connect_rect = pygame.Rect(WIDTH//2 + 20, HEIGHT - 80, 100, 50)
        manual_rect = pygame.Rect(WIDTH - 150, HEIGHT - 80, 100, 50)

        if back_rect.collidepoint(mouse_pos):
            self.state = "mode_select"
        elif refresh_rect.collidepoint(mouse_pos):
            self._scan_for_servers()
        elif manual_rect.collidepoint(mouse_pos):
            self.state = "connect"
        elif connect_rect.collidepoint(mouse_pos) and self.discovered_servers:
            # Conecta ao primeiro servidor da lista
            server_ip, server_port = self.discovered_servers[0]
            return self._attempt_connection(server_ip, server_port)

        # Clique em servidores da lista
        for i, (server_ip, server_port) in enumerate(self.discovered_servers):
            server_rect = pygame.Rect(WIDTH//2 - 200, 200 + i * 60, 400, 50)
            if server_rect.collidepoint(mouse_pos):
                return self._attempt_connection(server_ip, server_port)

        return None

    def _handle_connect_keys(self, event):
        """Gerencia teclas na conexão manual."""
        if event.key == pygame.K_ESCAPE:
            self.state = "server_list"
        elif event.key == pygame.K_TAB:
            self.input_field = "port" if self.input_field == "ip" else "ip"
        elif event.key == pygame.K_RETURN:
            try:
                port = int(self.server_port)
                return self._attempt_connection(self.server_ip, port)
            except ValueError:
                print("Porta inválida")
        elif event.key == pygame.K_BACKSPACE:
            if self.input_field == "ip" and self.server_ip:
                self.server_ip = self.server_ip[:-1]
            elif self.input_field == "port" and self.server_port:
                self.server_port = self.server_port[:-1]
        else:
            # Adiciona caractere
            char = event.unicode
            if char.isprintable():
                if self.input_field == "ip":
                    self.server_ip += char
                elif self.input_field == "port" and char.isdigit():
                    self.server_port += char

        return None

    def _handle_connect_mouse(self, event):
        """Gerencia cliques na conexão manual."""
        mouse_pos = pygame.mouse.get_pos()

        # Campos de entrada
        ip_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 - 40, 300, 40)
        port_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 + 20, 300, 40)

        if ip_rect.collidepoint(mouse_pos):
            self.input_field = "ip"
        elif port_rect.collidepoint(mouse_pos):
            self.input_field = "port"

        # Botões
        back_rect = pygame.Rect(50, HEIGHT - 80, 100, 50)
        connect_rect = pygame.Rect(WIDTH//2 - 75, HEIGHT//2 + 80, 150, 50)

        if back_rect.collidepoint(mouse_pos):
            self.state = "server_list"
        elif connect_rect.collidepoint(mouse_pos):
            try:
                port = int(self.server_port)
                return self._attempt_connection(self.server_ip, port)
            except ValueError:
                print("Porta inválida")

        return None

    def _scan_for_servers(self):
        """Inicia busca por servidores."""
        self.scanning = True
        self.discovered_servers = []

        # Em uma implementação real, isso seria feito em thread separada
        import threading

        def scan_thread():
            servers = ServerDiscovery.find_servers()
            self.discovered_servers = servers
            self.scanning = False

        thread = threading.Thread(target=scan_thread)
        thread.daemon = True
        thread.start()

    def _attempt_connection(self, host, port):
        """Tenta conectar ao servidor."""
        if network_client.connect(host, port):
            return ("multiplayer", host, port)
        else:
            print(f"Falha ao conectar a {host}:{port}")
            return None

    def draw(self, surface):
        """Desenha o menu multiplayer."""
        if self.state == "mode_select":
            self._draw_mode_select(surface)
        elif self.state == "server_list":
            self._draw_server_list(surface)
        elif self.state == "connect":
            self._draw_connect_screen(surface)

    def _draw_mode_select(self, surface):
        """Desenha seleção de modo de jogo."""
        # Fundo moderno
        self._draw_modern_background(surface)

        # Título
        ui.draw_text(surface, "MODO DE JOGO", ui.font_title, WHITE,
                    WIDTH//2, 100, center=True)

        ui.draw_text(surface, "ESCOLHA SUA MISSÃO", ui.font_large, UI_BLUE,
                    WIDTH//2, 150, center=True)

        # Opções
        options = [
            ("CAMPANHA SOLO", "Jogue contra IA"),
            ("OPERAÇÃO MULTIPLAYER", "Conecte a um servidor"),
            ("HOSPEDAR SERVIDOR", "Configure e crie servidor local")
        ]

        for i, (title, desc) in enumerate(options):
            y = HEIGHT//2 - 60 + i * 60

            # Fundo da opção
            option_rect = pygame.Rect(WIDTH//2 - 150, y, 300, 50)

            if i == self.selected_option:
                pygame.draw.rect(surface, (50, 100, 150), option_rect)
                pygame.draw.rect(surface, UI_BLUE, option_rect, 3)
            else:
                pygame.draw.rect(surface, (30, 30, 40), option_rect)
                pygame.draw.rect(surface, (80, 80, 100), option_rect, 2)

            # Texto
            color = WHITE if i == self.selected_option else (200, 200, 200)
            ui.draw_text(surface, title, ui.font_normal, color,
                        WIDTH//2, y + 15, center=True)
            ui.draw_text(surface, desc, ui.font_small, (150, 150, 150),
                        WIDTH//2, y + 35, center=True)

        # Botão voltar
        ui.draw_button(surface, "VOLTAR", 50, HEIGHT - 80, 100, 50, UI_RED, WHITE)

        # Instruções
        ui.draw_text(surface, "↑↓ para navegar | ENTER para selecionar | ESC para voltar",
                    ui.font_small, (150, 150, 170), WIDTH//2, HEIGHT - 30, center=True)

    def _draw_server_list(self, surface):
        """Desenha lista de servidores."""
        # Fundo moderno
        self._draw_modern_background(surface)

        # Título
        ui.draw_text(surface, "SERVIDORES DISPONÍVEIS", ui.font_title, WHITE,
                    WIDTH//2, 80, center=True)

        if self.scanning:
            ui.draw_text(surface, "Procurando servidores...", ui.font_large, UI_BLUE,
                        WIDTH//2, 140, center=True)
        elif not self.discovered_servers:
            ui.draw_text(surface, "Nenhum servidor encontrado", ui.font_large, UI_RED,
                        WIDTH//2, 140, center=True)
            ui.draw_text(surface, "Pressione F5 para atualizar ou C para conectar manualmente",
                        ui.font_normal, (150, 150, 170), WIDTH//2, 170, center=True)
        else:
            ui.draw_text(surface, f"{len(self.discovered_servers)} servidor(es) encontrado(s)",
                        ui.font_large, UI_GREEN, WIDTH//2, 140, center=True)

            # Lista de servidores
            for i, (server_ip, server_port) in enumerate(self.discovered_servers):
                y = 200 + i * 60

                server_rect = pygame.Rect(WIDTH//2 - 200, y, 400, 50)
                pygame.draw.rect(surface, (40, 60, 80), server_rect)
                pygame.draw.rect(surface, UI_BLUE, server_rect, 2)

                ui.draw_text(surface, f"{server_ip}:{server_port}", ui.font_normal, WHITE,
                            WIDTH//2, y + 15, center=True)
                ui.draw_text(surface, "Clique para conectar", ui.font_small, (150, 150, 170),
                            WIDTH//2, y + 35, center=True)

        # Botões
        ui.draw_button(surface, "VOLTAR", 50, HEIGHT - 80, 100, 50, UI_RED, WHITE)
        ui.draw_button(surface, "ATUALIZAR", WIDTH//2 - 100, HEIGHT - 80, 100, 50, UI_BLUE, WHITE)
        ui.draw_button(surface, "MANUAL", WIDTH - 150, HEIGHT - 80, 100, 50, UI_GREEN, WHITE)

        if self.discovered_servers:
            ui.draw_button(surface, "CONECTAR", WIDTH//2 + 20, HEIGHT - 80, 100, 50, UI_GREEN, WHITE)

    def _draw_connect_screen(self, surface):
        """Desenha tela de conexão manual."""
        # Fundo moderno
        self._draw_modern_background(surface)

        # Título
        ui.draw_text(surface, "CONEXÃO MANUAL", ui.font_title, WHITE,
                    WIDTH//2, 100, center=True)

        ui.draw_text(surface, "Digite o endereço do servidor", ui.font_large, UI_BLUE,
                    WIDTH//2, 150, center=True)

        # Campo IP
        ui.draw_text(surface, "Endereço IP:", ui.font_normal, WHITE,
                    WIDTH//2 - 150, HEIGHT//2 - 70, center=False)

        ip_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 - 40, 300, 40)
        border_color = UI_BLUE if self.input_field == "ip" else (100, 100, 100)
        pygame.draw.rect(surface, (30, 30, 40), ip_rect)
        pygame.draw.rect(surface, border_color, ip_rect, 2)

        ui.draw_text(surface, self.server_ip, ui.font_normal, WHITE,
                    ip_rect.centerx, ip_rect.centery, center=True)

        # Campo Porta
        ui.draw_text(surface, "Porta:", ui.font_normal, WHITE,
                    WIDTH//2 - 150, HEIGHT//2 - 10, center=False)

        port_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 + 20, 300, 40)
        border_color = UI_BLUE if self.input_field == "port" else (100, 100, 100)
        pygame.draw.rect(surface, (30, 30, 40), port_rect)
        pygame.draw.rect(surface, border_color, port_rect, 2)

        ui.draw_text(surface, self.server_port, ui.font_normal, WHITE,
                    port_rect.centerx, port_rect.centery, center=True)

        # Botões
        ui.draw_button(surface, "VOLTAR", 50, HEIGHT - 80, 100, 50, UI_RED, WHITE)
        ui.draw_button(surface, "CONECTAR", WIDTH//2 - 75, HEIGHT//2 + 80, 150, 50, UI_GREEN, WHITE)

        # Instruções
        ui.draw_text(surface, "TAB para alternar campos | ENTER para conectar",
                    ui.font_small, (150, 150, 170), WIDTH//2, HEIGHT - 30, center=True)

    def _draw_modern_background(self, surface):
        """Desenha fundo moderno."""
        # Gradiente
        for y in range(HEIGHT):
            color_factor = y / HEIGHT
            r = int(20 + color_factor * 15)
            g = int(25 + color_factor * 20)
            b = int(35 + color_factor * 25)
            pygame.draw.line(surface, (r, g, b), (0, y), (WIDTH, y))

        # Efeitos visuais
        current_time = time.time()
        for i in range(5):
            x = 100 + i * 150 + math.sin(current_time + i) * 30
            y = 200 + math.cos(current_time * 0.8 + i) * 40
            size = 2 + math.sin(current_time * 2 + i) * 1

            color = (100, 150, 200)
            pygame.draw.circle(surface, color, (int(x), int(y)), int(size))

# Instância global
multiplayer_menu = MultiplayerMenu()
