#!/usr/bin/env python3
# start_server.py

"""
Script para iniciar o servidor do Arena Battle Royale.
Execute este arquivo para hospedar um servidor local.
"""

import sys
import os

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from server import GameServer
from network_client import ServerDiscovery

def main():
    print("=" * 60)
    print("    ARENA BATTLE ROYALE - SERVIDOR DEDICADO")
    print("=" * 60)
    print()

    # Configurações padrão
    host = '0.0.0.0'
    port = 12345
    enable_bots = True
    game_mode = "pve"

    # Verifica argumentos da linha de comando
    if len(sys.argv) > 1:
        if "--help" in sys.argv:
            show_help()
            return
        if "--pvp" in sys.argv:
            game_mode = "pvp"
            enable_bots = False
        if "--no-bots" in sys.argv:
            enable_bots = False
        if "--port" in sys.argv:
            try:
                port_index = sys.argv.index("--port") + 1
                port = int(sys.argv[port_index])
            except (ValueError, IndexError):
                print("Porta inválida, usando padrão 12345")

    # Mostra IP local
    local_ip = ServerDiscovery.get_local_ip()
    print(f"IP local da máquina: {local_ip}")
    print(f"Outros jogadores devem conectar em: {local_ip}:{port}")
    print()

    print(f"Configurações do servidor:")
    print(f"  Modo: {'PvP Competitivo' if game_mode == 'pvp' else 'PvE Cooperativo'}")
    print(f"  Bots IA: {'Habilitados' if enable_bots else 'Desabilitados'}")
    print(f"  Porta: {port}")
    print()

    print("Pressione Ctrl+C para parar o servidor")
    print("Aguardando jogadores...")
    print("-" * 40)

    # Cria e inicia servidor
    server = GameServer(host, port, enable_bots, game_mode)

    try:
        server.start()
    except KeyboardInterrupt:
        print("\n" + "=" * 40)
        print("Servidor encerrado pelo usuário")
        print("Obrigado por jogar Arena Battle Royale!")
        print("=" * 40)
    except Exception as e:
        print(f"\nErro fatal no servidor: {e}")
        print("Verifique se a porta não está em uso")

def show_help():
    print("Uso: python start_server.py [opções]")
    print()
    print("Opções:")
    print("  --pvp          Modo PvP (jogadores vs jogadores)")
    print("  --no-bots      Desabilita bots IA")
    print("  --port PORTA   Define porta personalizada")
    print("  --help         Mostra esta ajuda")
    print()
    print("Exemplos:")
    print("  python start_server.py                    # PvE padrão com bots")
    print("  python start_server.py --pvp              # PvP sem bots")
    print("  python start_server.py --no-bots          # PvE sem bots")
    print("  python start_server.py --port 8080        # Porta personalizada")

if __name__ == "__main__":
    main()
