# src/camera.py

import pygame
from settings import WIDTH, HEIGHT, WORLD_WIDTH, WORLD_HEIGHT, CAMERA_SMOOTHNESS

class Camera:
    def __init__(self):
        """
        Inicializa a câmera que seguirá o jogador principal.
        """
        self.x = 0
        self.y = 0
        self.target_x = 0
        self.target_y = 0
        
    def update(self, target):
        """
        Atualiza a posição da câmera para seguir o alvo (jogador).
        target: objeto com atributos rect.centerx e rect.centery
        """
        # Calcula a posição desejada da câmera (centralizada no alvo)
        self.target_x = target.rect.centerx - WIDTH // 2
        self.target_y = target.rect.centery - HEIGHT // 2
        
        # Limita a câmera aos limites do mundo
        self.target_x = max(0, min(self.target_x, WORLD_WIDTH - WIDTH))
        self.target_y = max(0, min(self.target_y, WORLD_HEIGHT - HEIGHT))
        
        # Movimento suave da câmera
        self.x += (self.target_x - self.x) * CAMERA_SMOOTHNESS
        self.y += (self.target_y - self.y) * CAMERA_SMOOTHNESS
        
    def apply(self, rect):
        """
        Aplica o offset da câmera a um retângulo.
        Retorna um novo retângulo com a posição ajustada para a tela.
        """
        return pygame.Rect(rect.x - self.x, rect.y - self.y, rect.width, rect.height)
        
    def apply_pos(self, x, y):
        """
        Aplica o offset da câmera a uma posição (x, y).
        Retorna a posição ajustada para a tela.
        """
        return (x - self.x, y - self.y)
        
    def is_visible(self, rect):
        """
        Verifica se um retângulo está visível na tela atual.
        Útil para otimização - só desenhar objetos visíveis.
        """
        camera_rect = pygame.Rect(self.x, self.y, WIDTH, HEIGHT)
        return camera_rect.colliderect(rect)
