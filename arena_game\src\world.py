# src/world.py

import pygame
import random
from settings import WORLD_WIDTH, WORLD_HEIGHT, WHITE, GRAY, BLACK

class World:
    def __init__(self):
        """
        Inicializa o mundo urbano moderno do jogo.
        """
        self.width = WORLD_WIDTH
        self.height = WORLD_HEIGHT

        # Cria uma superfície para o fundo do mundo
        self.background = pygame.Surface((WORLD_WIDTH, WORLD_HEIGHT))
        self._create_modern_background()

    def _create_modern_background(self):
        """Cria um fundo urbano moderno e realista."""
        # Fundo base (asfalto urbano)
        self.background.fill((40, 40, 45))  # Cinza escuro urbano

        # Desenha ruas e calçadas
        self._draw_streets()

        # Adiciona elementos urbanos
        self._add_urban_elements()

        # Desenha bordas do mundo
        pygame.draw.rect(self.background, (20, 20, 20), (0, 0, WORLD_WIDTH, WORLD_HEIGHT), 8)

    def _draw_streets(self):
        """Desenha ruas e calçadas urbanas."""
        street_color = (35, 35, 40)  # Asfalto
        line_color = (200, 200, 50)  # Linhas amarelas
        sidewalk_color = (60, 60, 65)  # Calçada

        # Ruas horizontais
        for y in range(200, WORLD_HEIGHT, 400):
            # Rua principal
            pygame.draw.rect(self.background, street_color, (0, y, WORLD_WIDTH, 80))
            # Calçadas
            pygame.draw.rect(self.background, sidewalk_color, (0, y-20, WORLD_WIDTH, 20))
            pygame.draw.rect(self.background, sidewalk_color, (0, y+80, WORLD_WIDTH, 20))
            # Linhas da rua
            for x in range(0, WORLD_WIDTH, 100):
                pygame.draw.rect(self.background, line_color, (x, y+38, 50, 4))

        # Ruas verticais
        for x in range(300, WORLD_WIDTH, 500):
            # Rua principal
            pygame.draw.rect(self.background, street_color, (x, 0, 80, WORLD_HEIGHT))
            # Calçadas
            pygame.draw.rect(self.background, sidewalk_color, (x-20, 0, 20, WORLD_HEIGHT))
            pygame.draw.rect(self.background, sidewalk_color, (x+80, 0, 20, WORLD_HEIGHT))
            # Linhas da rua
            for y in range(0, WORLD_HEIGHT, 100):
                pygame.draw.rect(self.background, line_color, (x+38, y, 4, 50))

    def _add_urban_elements(self):
        """Adiciona elementos urbanos modernos."""
        # Prédios
        self._draw_buildings()

        # Veículos estacionados
        self._draw_vehicles()

        # Elementos urbanos (postes, lixeiras, etc.)
        self._draw_street_furniture()

    def _draw_buildings(self):
        """Desenha prédios urbanos."""
        building_positions = [
            (100, 100, 150, 200),
            (500, 150, 120, 180),
            (900, 80, 180, 250),
            (1300, 120, 140, 220),
            (1700, 90, 160, 240),
            (200, 600, 130, 190),
            (600, 650, 170, 210),
            (1100, 580, 150, 200),
            (1500, 620, 140, 180),
            (1900, 600, 120, 170),
        ]

        for x, y, w, h in building_positions:
            # Prédio principal
            building_color = random.choice([(60, 60, 80), (70, 70, 90), (50, 60, 70)])
            pygame.draw.rect(self.background, building_color, (x, y, w, h))
            pygame.draw.rect(self.background, (30, 30, 30), (x, y, w, h), 3)

            # Janelas
            for window_y in range(y + 20, y + h - 20, 25):
                for window_x in range(x + 15, x + w - 15, 20):
                    if random.random() > 0.3:  # 70% chance de janela
                        window_color = (255, 255, 100) if random.random() > 0.6 else (40, 40, 60)
                        pygame.draw.rect(self.background, window_color, (window_x, window_y, 8, 12))

            # Telhado
            roof_color = (80, 80, 100)
            pygame.draw.rect(self.background, roof_color, (x-5, y-10, w+10, 15))

    def _draw_vehicles(self):
        """Desenha veículos estacionados."""
        vehicle_positions = [
            (250, 280, 60, 30),
            (450, 280, 55, 28),
            (650, 280, 65, 32),
            (850, 680, 60, 30),
            (1050, 680, 58, 29),
            (1250, 680, 62, 31),
        ]

        for x, y, w, h in vehicle_positions:
            # Cor aleatória do carro
            car_color = random.choice([(150, 0, 0), (0, 0, 150), (100, 100, 100), (50, 50, 50), (0, 100, 0)])
            pygame.draw.rect(self.background, car_color, (x, y, w, h))
            pygame.draw.rect(self.background, (20, 20, 20), (x, y, w, h), 2)

            # Janelas
            pygame.draw.rect(self.background, (100, 150, 200), (x+5, y+5, w-10, h-10))

            # Rodas
            pygame.draw.circle(self.background, (20, 20, 20), (x+10, y+h-5), 6)
            pygame.draw.circle(self.background, (20, 20, 20), (x+w-10, y+h-5), 6)

    def _draw_street_furniture(self):
        """Desenha mobiliário urbano."""
        # Postes de luz
        streetlight_positions = [(150, 320), (350, 320), (550, 320), (750, 320), (950, 720), (1150, 720)]

        for x, y in streetlight_positions:
            # Poste
            pygame.draw.rect(self.background, (120, 120, 120), (x, y, 8, 40))
            # Luz
            pygame.draw.circle(self.background, (255, 255, 150), (x+4, y), 12)
            pygame.draw.circle(self.background, (200, 200, 100), (x+4, y), 8)

        # Lixeiras
        trash_positions = [(200, 350), (400, 350), (600, 350), (800, 750), (1000, 750)]

        for x, y in trash_positions:
            pygame.draw.rect(self.background, (80, 80, 80), (x, y, 15, 20))
            pygame.draw.rect(self.background, (60, 60, 60), (x, y, 15, 5))  # Tampa

    def draw(self, surface, camera):
        """
        Desenha a parte visível do mundo na tela.
        """
        # Calcula qual parte do fundo deve ser desenhada
        camera_rect = pygame.Rect(camera.x, camera.y, surface.get_width(), surface.get_height())

        # Desenha a parte visível do fundo
        surface.blit(self.background, (0, 0), camera_rect)
