# src/character_select.py

import pygame
import math
import time
from settings import WIDTH, HEIGHT, WHITE, BLACK, UI_BLUE, UI_GREEN, UI_RED, UI_DARK_GRAY, UI_LIGHT_GRAY
from ui import ui
from characters import CHARACTERS, get_character_list

class CharacterSelect:
    def __init__(self):
        """
        Tela de seleção de personagens.
        """
        self.selected_character = 0
        self.character_ids = get_character_list()
        self.characters = list(CHARACTERS.values())

    def handle_events(self, events):
        """
        Manipula eventos da tela de seleção.
        """
        for event in events:
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_LEFT:
                    self.selected_character = (self.selected_character - 1) % len(self.characters)
                elif event.key == pygame.K_RIGHT:
                    self.selected_character = (self.selected_character + 1) % len(self.characters)
                elif event.key == pygame.K_RETURN:
                    return self.character_ids[self.selected_character]
                elif event.key == pygame.K_ESCAPE:
                    return "back"

            elif event.type == pygame.MOUSEBUTTONDOWN:
                mouse_pos = pygame.mouse.get_pos()

                # Verifica clique nos botões de navegação
                left_button = pygame.Rect(50, HEIGHT//2 - 25, 50, 50)
                right_button = pygame.Rect(WIDTH - 100, HEIGHT//2 - 25, 50, 50)
                select_button = pygame.Rect(WIDTH//2 - 100, HEIGHT - 100, 200, 50)
                back_button = pygame.Rect(50, HEIGHT - 100, 100, 50)

                if left_button.collidepoint(mouse_pos):
                    self.selected_character = (self.selected_character - 1) % len(self.characters)
                elif right_button.collidepoint(mouse_pos):
                    self.selected_character = (self.selected_character + 1) % len(self.characters)
                elif select_button.collidepoint(mouse_pos):
                    return self.character_ids[self.selected_character]
                elif back_button.collidepoint(mouse_pos):
                    return "back"

        return None

    def draw(self, surface):
        """
        Desenha a tela de seleção de personagens moderna e futurista.
        """
        # Fundo moderno
        self._draw_modern_background(surface)

        # Título principal
        self._draw_title(surface)

        # Painel principal do personagem
        self._draw_character_panel(surface)

        # Navegação e controles
        self._draw_navigation(surface)

        # Efeitos visuais
        self._draw_visual_effects(surface)

    def _draw_modern_background(self, surface):
        """Desenha fundo moderno com gradiente militar."""
        # Gradiente de fundo
        for y in range(HEIGHT):
            color_factor = y / HEIGHT
            r = int(15 + color_factor * 20)  # 15 -> 35
            g = int(20 + color_factor * 25)  # 20 -> 45
            b = int(30 + color_factor * 30)  # 30 -> 60
            pygame.draw.line(surface, (r, g, b), (0, y), (WIDTH, y))

        # Grade hexagonal futurista
        hex_color = (40, 50, 65)
        for x in range(0, WIDTH, 60):
            for y in range(0, HEIGHT, 52):
                offset_x = 30 if (y // 52) % 2 else 0
                self._draw_hexagon(surface, x + offset_x, y, 25, hex_color)

    def _draw_hexagon(self, surface, x, y, size, color):
        """Desenha um hexágono."""
        points = []
        for i in range(6):
            angle = math.pi / 3 * i
            px = x + size * math.cos(angle)
            py = y + size * math.sin(angle)
            points.append((px, py))
        pygame.draw.polygon(surface, color, points, 1)

    def _draw_title(self, surface):
        """Desenha título principal."""
        # Sombra do título
        ui.draw_text(surface, "SELEÇÃO DE OPERADOR", ui.font_title, (10, 10, 10),
                    WIDTH//2 + 3, 53, center=True)

        # Título principal
        ui.draw_text(surface, "SELEÇÃO DE OPERADOR", ui.font_title, (255, 255, 255),
                    WIDTH//2, 50, center=True)

        # Subtítulo
        ui.draw_text(surface, "ESCOLHA SEU ESPECIALISTA", ui.font_large, (100, 200, 255),
                    WIDTH//2, 90, center=True)

        # Linha decorativa
        pygame.draw.line(surface, (100, 200, 255), (WIDTH//2 - 150, 110), (WIDTH//2 + 150, 110), 3)

    def _draw_character_panel(self, surface):
        """Desenha painel principal do personagem."""
        current_char = self.characters[self.selected_character]

        # Painel principal
        panel_rect = pygame.Rect(WIDTH//2 - 250, HEIGHT//2 - 120, 500, 280)

        # Fundo do painel
        panel_color = (30, 40, 55)
        pygame.draw.rect(surface, panel_color, panel_rect)

        # Borda com cor baseada na especialidade
        border_color = self._get_character_color(current_char)
        pygame.draw.rect(surface, border_color, panel_rect, 4)

        # Painel esquerdo - Sprite e info básica
        self._draw_character_info(surface, current_char, WIDTH//2 - 180, HEIGHT//2 - 80)

        # Painel direito - Estatísticas
        self._draw_character_stats(surface, current_char, WIDTH//2 + 20, HEIGHT//2 - 80)

        # Linha separadora vertical
        pygame.draw.line(surface, border_color, (WIDTH//2, HEIGHT//2 - 100), (WIDTH//2, HEIGHT//2 + 140), 2)

    def _draw_character_info(self, surface, character, x, y):
        """Desenha informações básicas do personagem."""
        # Sprite ampliado com efeito de brilho
        char_sprite = pygame.transform.scale(character.sprite, (100, 100))
        sprite_rect = char_sprite.get_rect(center=(x, y))

        # Efeito de brilho atrás do sprite
        glow_color = self._get_character_color(character)
        pygame.draw.circle(surface, (*glow_color, 50), (x, y), 60, 3)

        surface.blit(char_sprite, sprite_rect)

        # Nome do personagem
        ui.draw_text(surface, character.name, ui.font_large, (255, 255, 255),
                    x, y + 70, center=True)

        # Descrição
        description_lines = self._wrap_text(character.description, 25)
        for i, line in enumerate(description_lines):
            ui.draw_text(surface, line, ui.font_small, (200, 200, 200),
                        x, y + 100 + i * 18, center=True)

        # Backstory (se disponível)
        if hasattr(character, 'backstory') and character.backstory:
            backstory_lines = self._wrap_text(character.backstory, 25)
            for i, line in enumerate(backstory_lines[:2]):  # Máximo 2 linhas
                ui.draw_text(surface, line, ui.font_small, (150, 150, 170),
                            x, y + 140 + i * 16, center=True)

    def _draw_character_stats(self, surface, character, x, y):
        """Desenha estatísticas do personagem."""
        # Título das estatísticas
        ui.draw_text(surface, "ESPECIFICAÇÕES TÉCNICAS", ui.font_normal, (200, 200, 200),
                    x + 80, y, center=True)

        # Estatísticas com barras visuais
        stats_data = [
            ("VELOCIDADE", character.stats['speed'], 7, (100, 255, 100)),
            ("RESISTÊNCIA", character.stats['health'], 160, (100, 200, 255)),
            ("POTÊNCIA DE FOGO", character.stats['damage'], 50, (255, 150, 100))
        ]

        for i, (label, value, max_value, color) in enumerate(stats_data):
            stat_y = y + 40 + i * 50

            # Label
            ui.draw_text(surface, label, ui.font_small, (180, 180, 180),
                        x, stat_y, center=False)

            # Valor numérico
            ui.draw_text(surface, str(value), ui.font_small, (255, 255, 255),
                        x + 140, stat_y, center=False)

            # Barra de progresso
            bar_rect = pygame.Rect(x, stat_y + 15, 150, 8)
            pygame.draw.rect(surface, (40, 40, 50), bar_rect)

            # Preenchimento da barra
            fill_width = int((value / max_value) * 150)
            fill_rect = pygame.Rect(x, stat_y + 15, fill_width, 8)
            pygame.draw.rect(surface, color, fill_rect)

            # Borda da barra
            pygame.draw.rect(surface, (100, 100, 120), bar_rect, 1)

        # Habilidade especial
        special_y = y + 180
        ui.draw_text(surface, "HABILIDADE ESPECIAL", ui.font_small, (200, 200, 200),
                    x + 80, special_y, center=True)

        special_abilities = {
            "tactical": "🎯 OPERAÇÕES TÁTICAS",
            "cyber": "🤖 TECNOLOGIA AVANÇADA",
            "armor": "🛡️ BLINDAGEM PESADA",
            "stealth": "🥷 INFILTRAÇÃO",
            "healing": "❤️ SUPORTE MÉDICO",
            "explosives": "💥 DEMOLIÇÃO"
        }

        for ability, description in special_abilities.items():
            if character.stats.get(ability):
                color = self._get_character_color(character)
                ui.draw_text(surface, description, ui.font_small, color,
                            x + 80, special_y + 25, center=True)
                break

    def _draw_navigation(self, surface):
        """Desenha controles de navegação."""
        # Setas de navegação modernas
        left_rect = pygame.Rect(50, HEIGHT//2 - 30, 60, 60)
        right_rect = pygame.Rect(WIDTH - 110, HEIGHT//2 - 30, 60, 60)

        # Seta esquerda
        pygame.draw.rect(surface, (50, 70, 90), left_rect)
        pygame.draw.rect(surface, (100, 200, 255), left_rect, 3)
        ui.draw_text(surface, "◀", ui.font_large, (255, 255, 255),
                    left_rect.centerx, left_rect.centery, center=True)

        # Seta direita
        pygame.draw.rect(surface, (50, 70, 90), right_rect)
        pygame.draw.rect(surface, (100, 200, 255), right_rect, 3)
        ui.draw_text(surface, "▶", ui.font_large, (255, 255, 255),
                    right_rect.centerx, right_rect.centery, center=True)

        # Indicador de posição
        indicator_text = f"OPERADOR {self.selected_character + 1} DE {len(self.characters)}"
        ui.draw_text(surface, indicator_text, ui.font_normal, (150, 150, 170),
                    WIDTH//2, HEIGHT//2 + 180, center=True)

        # Botões de ação
        self._draw_action_buttons(surface)

        # Preview de outros personagens
        self._draw_character_preview(surface)

    def _draw_action_buttons(self, surface):
        """Desenha botões de ação."""
        # Botão voltar
        back_rect = pygame.Rect(50, HEIGHT - 80, 120, 50)
        pygame.draw.rect(surface, (150, 50, 50), back_rect)
        pygame.draw.rect(surface, (255, 100, 100), back_rect, 3)
        ui.draw_text(surface, "ABORTAR", ui.font_normal, (255, 255, 255),
                    back_rect.centerx, back_rect.centery - 5, center=True)
        ui.draw_text(surface, "(ESC)", ui.font_small, (200, 200, 200),
                    back_rect.centerx, back_rect.centery + 15, center=True)

        # Botão selecionar
        select_rect = pygame.Rect(WIDTH//2 - 100, HEIGHT - 80, 200, 50)
        pygame.draw.rect(surface, (50, 150, 50), select_rect)
        pygame.draw.rect(surface, (100, 255, 100), select_rect, 3)
        ui.draw_text(surface, "CONFIRMAR SELEÇÃO", ui.font_normal, (255, 255, 255),
                    select_rect.centerx, select_rect.centery - 5, center=True)
        ui.draw_text(surface, "(ENTER)", ui.font_small, (200, 200, 200),
                    select_rect.centerx, select_rect.centery + 15, center=True)

    def _draw_character_preview(self, surface):
        """Desenha preview de todos os personagens."""
        preview_y = 140
        preview_spacing = 90
        start_x = WIDTH//2 - (len(self.characters) * preview_spacing) // 2

        for i, char in enumerate(self.characters):
            x = start_x + i * preview_spacing

            # Fundo do preview
            preview_rect = pygame.Rect(x - 35, preview_y - 35, 70, 70)

            if i == self.selected_character:
                # Personagem selecionado
                pygame.draw.rect(surface, (50, 70, 90), preview_rect)
                pygame.draw.rect(surface, self._get_character_color(char), preview_rect, 4)
            else:
                # Outros personagens
                pygame.draw.rect(surface, (30, 30, 40), preview_rect)
                pygame.draw.rect(surface, (80, 80, 100), preview_rect, 2)

            # Sprite pequeno
            small_sprite = pygame.transform.scale(char.sprite, (50, 50))
            sprite_rect = small_sprite.get_rect(center=(x, preview_y))
            surface.blit(small_sprite, sprite_rect)

            # Nome abreviado
            short_name = char.name.split()[0]
            color = (255, 255, 255) if i == self.selected_character else (150, 150, 150)
            ui.draw_text(surface, short_name, ui.font_small, color,
                        x, preview_y + 45, center=True)

    def _draw_visual_effects(self, surface):
        """Desenha efeitos visuais animados."""
        current_time = time.time()

        # Partículas flutuantes
        for i in range(6):
            x = 100 + i * 150 + math.sin(current_time + i) * 30
            y = 300 + math.cos(current_time * 0.8 + i) * 40
            size = 2 + math.sin(current_time * 2 + i) * 1

            alpha = int(128 + math.sin(current_time * 3 + i) * 127)
            color = (
                int(100 * alpha / 255),
                int(200 * alpha / 255),
                int(255 * alpha / 255)
            )
            pygame.draw.circle(surface, color, (int(x), int(y)), int(size))

        # Linhas de energia
        for i in range(3):
            start_x = 0
            end_x = WIDTH
            y = 200 + i * 150 + math.sin(current_time + i * 2) * 10

            line_color = (50 + i * 20, 100 + i * 30, 150 + i * 40)
            pygame.draw.line(surface, line_color, (start_x, int(y)), (end_x, int(y)), 1)

    def _get_character_color(self, character):
        """Retorna cor baseada no tipo de personagem."""
        if character.stats.get("tactical"):
            return (100, 255, 100)  # Verde
        elif character.stats.get("cyber"):
            return (100, 200, 255)  # Azul
        elif character.stats.get("armor"):
            return (255, 200, 100)  # Laranja
        elif character.stats.get("stealth"):
            return (200, 100, 255)  # Roxo
        elif character.stats.get("healing"):
            return (255, 100, 100)  # Vermelho
        elif character.stats.get("explosives"):
            return (255, 255, 100)  # Amarelo
        else:
            return (150, 150, 150)  # Cinza padrão

    def _wrap_text(self, text, max_chars):
        """Quebra texto em linhas."""
        words = text.split()
        lines = []
        current_line = ""

        for word in words:
            if len(current_line + word) <= max_chars:
                current_line += word + " "
            else:
                if current_line:
                    lines.append(current_line.strip())
                current_line = word + " "

        if current_line:
            lines.append(current_line.strip())

        return lines

# Instância global
character_select = CharacterSelect()
