#!/usr/bin/env python3
# start_server.py

"""
Script para iniciar o servidor do Arena Battle Royale.
Execute este arquivo para hospedar um servidor local.
"""

import sys
import os

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from server import GameServer
from network_client import ServerDiscovery

def main():
    print("=" * 60)
    print("    ARENA BATTLE ROYALE - SERVIDOR DEDICADO")
    print("=" * 60)
    print()
    
    # Mostra IP local
    local_ip = ServerDiscovery.get_local_ip()
    print(f"IP local da máquina: {local_ip}")
    print(f"Outros jogadores devem conectar em: {local_ip}:12345")
    print()
    
    # Configurações do servidor
    host = '0.0.0.0'  # Aceita conexões de qualquer IP
    port = 12345
    
    print(f"Iniciando servidor em {host}:{port}")
    print("Pressione Ctrl+C para parar o servidor")
    print()
    print("Aguardando jogadores...")
    print("-" * 40)
    
    # Cria e inicia servidor
    server = GameServer(host, port)
    
    try:
        server.start()
    except KeyboardInterrupt:
        print("\n" + "=" * 40)
        print("Servidor encerrado pelo usuário")
        print("Obrigado por jogar Arena Battle Royale!")
        print("=" * 40)
    except Exception as e:
        print(f"\nErro fatal no servidor: {e}")
        print("Verifique se a porta 12345 não está em uso")

if __name__ == "__main__":
    main()
