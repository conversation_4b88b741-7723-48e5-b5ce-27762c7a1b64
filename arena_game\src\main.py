# src/main.py

import pygame
import sys
import os
import random
from settings import WIDTH, HEIGHT, FPS, WHITE, WORLD_WIDTH, WORLD_HEIGHT, NUM_BOTS
from entities.player import Player
from entities.bot import Bot
from camera import Camera
from world import World
from utils import draw_text, draw_minimap

def main():
    pygame.init()
    screen = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("Arena Battle Royale - Mundo Expandido")
    clock = pygame.time.Clock()

    # --- Inicialização dos sistemas ---
    world = World()
    camera = Camera()

    # --- Instanciação do jogador principal ---
    player = Player(
        x=WORLD_WIDTH // 2,
        y=WORLD_HEIGHT // 2,
        sprite_path=os.path.join("assets", "player1.png"),
        controls={
            'up': pygame.K_w,
            'down': pygame.K_s,
            'left': pygame.K_a,
            'right': pygame.K_d,
            'shoot': pygame.K_SPACE,
            'bullet_dir': (1, 0)   # dispara para a direita
        }
    )

    # --- Criação dos inimigos (bots) ---
    bots = []
    for i in range(NUM_BOTS):
        # Posiciona bots aleatoriamente no mundo
        bot_x = random.randint(100, WORLD_WIDTH - 100)
        bot_y = random.randint(100, WORLD_HEIGHT - 100)

        # Evita spawnar muito perto do jogador
        while abs(bot_x - player.rect.x) < 200 and abs(bot_y - player.rect.y) < 200:
            bot_x = random.randint(100, WORLD_WIDTH - 100)
            bot_y = random.randint(100, WORLD_HEIGHT - 100)

        bot = Bot(
            x=bot_x,
            y=bot_y,
            sprite_path=os.path.join("assets", "player2.png"),
            target=player
        )
        bots.append(bot)

    running = True
    winner = None
    bots_defeated = 0

    while running:
        clock.tick(FPS)
        current_time = pygame.time.get_ticks()

        # --- Eventos de fechamento ---
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False

        # --- Captura estado atual do teclado ---
        keys = pygame.key.get_pressed()

        # --- Movimentação / Disparo do Jogador ---
        player.handle_movement(keys)
        player.handle_shooting(keys, current_time)

        # --- Atualiza Balas do Jogador ---
        player.update_bullets()

        # --- Atualiza Bots ---
        for bot in bots[:]:
            if bot.life > 0:
                bot.update(current_time)

        # --- Checa Colisões: balas do jogador atingindo bots ---
        for bullet in player.bullets[:]:
            for bot in bots:
                if bot.life > 0 and bullet.rect.colliderect(bot.rect):
                    player.bullets.remove(bullet)
                    bot.life -= 1
                    if bot.life <= 0:
                        bots_defeated += 1
                    break

        # --- Checa Colisões: balas dos bots atingindo o jogador ---
        for bot in bots:
            if bot.life > 0:
                for bullet in bot.bullets[:]:
                    if bullet.rect.colliderect(player.rect):
                        bot.bullets.remove(bullet)
                        player.life -= 1

        # --- Verifica condição de vitória/derrota ---
        if player.life <= 0:
            winner = "Você perdeu!"
            running = False
        elif bots_defeated >= NUM_BOTS:
            winner = "Você venceu!"
            running = False

        # --- Atualiza Câmera ---
        camera.update(player)

        # --- Desenho na Tela ---
        # Desenha o mundo
        world.draw(screen, camera)

        # Desenha o jogador
        player.draw(screen, camera)

        # Desenha bots vivos
        for bot in bots:
            if bot.life > 0:
                bot.draw(screen, camera)

        # --- Interface do usuário (UI) ---
        # Desenha informações do jogador
        draw_text(screen, f"Vida: {player.life}", 20, 10, 10)
        draw_text(screen, f"Inimigos: {NUM_BOTS - bots_defeated}/{NUM_BOTS}", 20, 10, 40)

        # Desenha minimapa
        draw_minimap(screen, camera, player, bots, world)

        # Desenha coordenadas (debug)
        draw_text(screen, f"Pos: ({int(player.rect.x)}, {int(player.rect.y)})", 16, 10, HEIGHT - 30)

        pygame.display.flip()

    # --- Tela de vitória e espera por reinício ou saída ---
    screen.fill(WHITE)
    draw_text(screen, f"{winner} venceu!", 40, WIDTH // 2 - 120, HEIGHT // 2 - 20)
    draw_text(screen, "Pressione R para reiniciar ou Q para sair", 24, WIDTH // 2 - 200, HEIGHT // 2 + 30)
    pygame.display.flip()

    waiting = True
    while waiting:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                waiting = False
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r:
                    main()  # reinicia o jogo recursivamente
                    return
                if event.key == pygame.K_q:
                    waiting = False

    pygame.quit()
    sys.exit()


if __name__ == "__main__":
    main()
