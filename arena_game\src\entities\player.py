# src/entities/player.py

import pygame
import os
from settings import PLAYER_SPEED, PLAYER_WIDTH, PLAYER_HEIGHT, PLAYER_LIFE, MAX_BULLETS, WORLD_WIDTH, WORLD_HEIGHT
from entities.bullet import Bullet

class Player:
    def __init__(self, x, y, sprite_path, controls):
        """
        x, y: posição inicial (pixels) do topo-esquerda do jogador
        sprite_path: caminho relativo para o sprite do jogador (ex.: "assets/player1.png")
        controls: dicionário contendo as teclas de movimento e tiro:
                  {
                    'up': pygame.K_w,
                    'down': pygame.K_s,
                    'left': pygame.K_a,
                    'right': pygame.K_d,
                    'shoot': pygame.K_SPACE,
                    'bullet_dir': (1, 0)      # direção padrão dos disparos
                  }
        """
        #--- Carrega sprite, ou fallback para retângulo colorido se não existir arquivo ---#
        try:
            img = pygame.image.load(sprite_path).convert_alpha()
            self.image = pygame.transform.scale(img, (PLAYER_WIDTH, PLAYER_HEIGHT))
        except (pygame.error, FileNotFoundError):
            # Se arquivo não existe (placeholder), cria uma Surface retangular
            self.image = pygame.Surface((PLAYER_WIDTH, PLAYER_HEIGHT))
            self.image.fill((0, 0, 255))  # jogador em azul por padrão

        self.rect = self.image.get_rect(topleft=(x, y))
        self.controls = controls
        self.life = PLAYER_LIFE
        self.bullets = []          # lista de instâncias de Bullet
        self.last_shot = 0         # timestamp (ms) do último disparo

        # Direção do movimento para tiro
        self.facing_direction = (1, 0)  # direção padrão (direita)
        self.last_movement_direction = (1, 0)  # última direção de movimento

    def handle_movement(self, keys_pressed):
        """Move o jogador de acordo com as teclas pressionadas e atualiza direção."""
        movement_x = 0
        movement_y = 0

        if keys_pressed[self.controls['up']] and self.rect.top > 0:
            self.rect.y -= PLAYER_SPEED
            movement_y = -1
        if keys_pressed[self.controls['down']] and self.rect.bottom < WORLD_HEIGHT:
            self.rect.y += PLAYER_SPEED
            movement_y = 1
        if keys_pressed[self.controls['left']] and self.rect.left > 0:
            self.rect.x -= PLAYER_SPEED
            movement_x = -1
        if keys_pressed[self.controls['right']] and self.rect.right < WORLD_WIDTH:
            self.rect.x += PLAYER_SPEED
            movement_x = 1

        # Atualiza a direção de movimento se houve movimento
        if movement_x != 0 or movement_y != 0:
            self.last_movement_direction = (movement_x, movement_y)
            self.facing_direction = (movement_x, movement_y)

    def handle_shooting(self, keys_pressed, current_time, audio_manager=None):
        """
        Se a tecla de atirar estiver pressionada, cria uma bala na direção do movimento.
        o jogador não exceda MAX_BULLETS e respeite um intervalo mínimo (300ms).
        """
        if (keys_pressed[self.controls['shoot']] and
            len(self.bullets) < MAX_BULLETS and
            current_time - self.last_shot > 300):
            # Cria bala no centro do jogador
            bx = self.rect.centerx
            by = self.rect.centery

            # Usa a direção do movimento atual, ou a última direção se parado
            direction = self.facing_direction

            bullet = Bullet(bx, by, direction)
            self.bullets.append(bullet)
            self.last_shot = current_time

            # Toca o som do tiro
            if audio_manager:
                audio_manager.play_shoot_sound()

    def update_bullets(self):
        """
        Atualiza todas as balas: move e remove aquelas que saíram do mundo.
        """
        for bullet in self.bullets[:]:
            bullet.move()
            # Se a bala estiver fora do mundo, remove
            world_rect = pygame.Rect(0, 0, WORLD_WIDTH, WORLD_HEIGHT)
            if not world_rect.colliderect(bullet.rect):
                self.bullets.remove(bullet)

    def draw(self, surface, camera=None):
        """Desenha o sprite do jogador e todas as balas na tela."""
        if camera:
            # Só desenha se estiver visível na câmera
            if camera.is_visible(self.rect):
                screen_rect = camera.apply(self.rect)
                surface.blit(self.image, screen_rect)
            # Desenha balas visíveis
            for bullet in self.bullets:
                if camera.is_visible(bullet.rect):
                    bullet.draw(surface, camera)
        else:
            # Modo compatibilidade (sem câmera)
            surface.blit(self.image, self.rect)
            for bullet in self.bullets:
                bullet.draw(surface)
