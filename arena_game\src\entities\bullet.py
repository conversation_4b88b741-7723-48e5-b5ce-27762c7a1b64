# src/entities/bullet.py

import pygame
from settings import BULLET_WIDTH, BULLET_HEIGHT, BULLET_SPEED

class Bullet:
    def __init__(self, x, y, direction):
        """
        x, y: posição inicial (centro) da bala
        direction: tupla (dx, dy) com direção normalizada.
                   Exemplos: (1, 0) → direita; (-1, 0) → esquerda; (0, 1) → baixo; etc.
        """
        # Cria uma Surface retangular simples para a bala
        self.image = pygame.Surface((BULLET_WIDTH, BULLET_HEIGHT))
        self.image.fill((255, 0, 0))  # bala vermelha
        self.rect = self.image.get_rect(center=(x, y))
        self.dx, self.dy = direction

    def move(self):
        """Move a bala de acordo com a direção e velocidade definida."""
        self.rect.x += int(self.dx * BULLET_SPEED)
        self.rect.y += int(self.dy * BULLET_SPEED)

    def draw(self, surface, camera=None):
        """Desenha a bala na superfície passada."""
        if camera:
            screen_rect = camera.apply(self.rect)
            surface.blit(self.image, screen_rect)
        else:
            surface.blit(self.image, self.rect)
