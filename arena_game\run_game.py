#!/usr/bin/env python3
# run_game.py - Launcher para Arena Battle Royale

"""
Launcher principal para o Arena Battle Royale.
Execute este arquivo para iniciar o jogo.
"""

import sys
import os

# Adiciona o diretório src ao path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

# Muda para o diretório src para recursos funcionarem
os.chdir(src_dir)

def main():
    print("=" * 60)
    print("    🎮 ARENA BATTLE ROYALE - TACTICAL COMBAT 🎮")
    print("=" * 60)
    print()
    print("🎯 Mira 360° com mouse")
    print("⚡ Skills únicas por personagem (B, N, M)")
    print("🌐 Multiplayer local disponível")
    print("🎨 Gráficos modernos e táticos")
    print()
    print("Iniciando jogo...")
    print("-" * 40)
    
    try:
        # Importa e executa o jogo
        from main import main as game_main
        game_main()
    except ImportError as e:
        print(f"❌ Erro ao importar módulos do jogo: {e}")
        print("Verifique se todos os arquivos estão presentes na pasta src/")
    except Exception as e:
        print(f"❌ Erro ao executar o jogo: {e}")
        print("Verifique se o pygame está instalado: pip install pygame")

if __name__ == "__main__":
    main()
