# src/server.py

import socket
import threading
import json
import time
import random
from settings import WORLD_WIDTH, WORLD_HEIGHT, NUM_BOTS
from enemy_types import create_enemy_squad

class GameServer:
    def __init__(self, host='0.0.0.0', port=12345, enable_bots=True, game_mode="pve"):
        """
        Servidor dedicado para o Battle Royale multiplayer.

        Args:
            host: IP do servidor
            port: Porta do servidor
            enable_bots: Se deve spawnar bots IA
            game_mode: "pve" (cooperativo) ou "pvp" (jogadores vs jogadores)
        """
        self.host = host
        self.port = port
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

        # Configurações do jogo
        self.enable_bots = enable_bots
        self.game_mode = game_mode  # "pve" ou "pvp"
        self.max_players = 4
        self.min_players = 1 if enable_bots else 2  # PvP precisa de pelo menos 2 jogadores

        # Estado do jogo
        self.players = {}  # {player_id: player_data}
        self.bots = {}     # {bot_id: bot_data}
        self.bullets = {}  # {bullet_id: bullet_data}
        self.game_state = "waiting"  # waiting, playing, game_over
        self.next_id = 1

        # Controle de tempo
        self.last_update = time.time()
        self.tick_rate = 60  # 60 FPS

        print(f"Servidor iniciado em {host}:{port}")
        print(f"Modo: {'PvE Cooperativo' if game_mode == 'pve' else 'PvP Competitivo'}")
        print(f"Bots IA: {'Habilitados' if enable_bots else 'Desabilitados'}")
        print(f"Jogadores: {self.min_players}-{self.max_players}")

    def start(self):
        """Inicia o servidor."""
        try:
            self.socket.bind((self.host, self.port))
            self.socket.listen(self.max_players)
            print(f"Servidor ouvindo em {self.host}:{self.port}")

            # Thread para atualização do jogo
            game_thread = threading.Thread(target=self.game_loop)
            game_thread.daemon = True
            game_thread.start()

            # Loop principal para aceitar conexões
            while True:
                client_socket, address = self.socket.accept()
                print(f"Nova conexão de {address}")

                # Thread para cada cliente
                client_thread = threading.Thread(
                    target=self.handle_client,
                    args=(client_socket, address)
                )
                client_thread.daemon = True
                client_thread.start()

        except Exception as e:
            print(f"Erro no servidor: {e}")
        finally:
            self.socket.close()

    def handle_client(self, client_socket, address):
        """Gerencia um cliente conectado."""
        player_id = None

        try:
            while True:
                data = client_socket.recv(1024).decode('utf-8')
                if not data:
                    break

                try:
                    message = json.loads(data)
                    response = self.process_message(message, address)

                    if message.get('type') == 'join' and response.get('success'):
                        player_id = response.get('player_id')

                    # Envia resposta
                    client_socket.send(json.dumps(response).encode('utf-8'))

                except json.JSONDecodeError:
                    print(f"Mensagem inválida de {address}: {data}")

        except Exception as e:
            print(f"Erro com cliente {address}: {e}")
        finally:
            # Remove jogador quando desconecta
            if player_id and player_id in self.players:
                del self.players[player_id]
                print(f"Jogador {player_id} desconectou")
                self.broadcast_player_left(player_id)

            client_socket.close()

    def process_message(self, message, address):
        """Processa mensagens dos clientes."""
        msg_type = message.get('type')

        if msg_type == 'join':
            return self.handle_join(message, address)
        elif msg_type == 'update_player':
            return self.handle_player_update(message)
        elif msg_type == 'shoot':
            return self.handle_shoot(message)
        elif msg_type == 'get_game_state':
            return self.get_full_game_state()
        else:
            return {'success': False, 'error': 'Tipo de mensagem desconhecido'}

    def handle_join(self, message, address):
        """Gerencia entrada de novo jogador."""
        if len(self.players) >= self.max_players:
            return {'success': False, 'error': 'Servidor lotado'}

        player_id = self.next_id
        self.next_id += 1

        # Posição inicial aleatória
        spawn_x = random.randint(100, WORLD_WIDTH - 100)
        spawn_y = random.randint(100, WORLD_HEIGHT - 100)

        # Dados do jogador
        player_data = {
            'id': player_id,
            'name': message.get('name', f'Player{player_id}'),
            'character': message.get('character', 'tactical_operator'),
            'x': spawn_x,
            'y': spawn_y,
            'health': 100,
            'max_health': 100,
            'ammo': 200,
            'kills': 0,
            'deaths': 0,
            'connected_at': time.time(),
            'last_update': time.time()
        }

        self.players[player_id] = player_data

        # Inicia jogo se atingir número mínimo de jogadores
        if len(self.players) >= self.min_players and self.game_state == "waiting":
            self.start_game()

        print(f"Jogador {player_data['name']} entrou (ID: {player_id})")

        return {
            'success': True,
            'player_id': player_id,
            'spawn_x': spawn_x,
            'spawn_y': spawn_y,
            'game_state': self.game_state
        }

    def handle_player_update(self, message):
        """Atualiza posição e estado do jogador."""
        player_id = message.get('player_id')

        if player_id not in self.players:
            return {'success': False, 'error': 'Jogador não encontrado'}

        # Atualiza dados do jogador
        player = self.players[player_id]
        player['x'] = message.get('x', player['x'])
        player['y'] = message.get('y', player['y'])
        player['health'] = message.get('health', player['health'])
        player['ammo'] = message.get('ammo', player['ammo'])
        player['last_update'] = time.time()

        return {'success': True}

    def handle_shoot(self, message):
        """Gerencia tiros dos jogadores."""
        player_id = message.get('player_id')

        if player_id not in self.players:
            return {'success': False, 'error': 'Jogador não encontrado'}

        # Cria nova bala
        bullet_id = f"bullet_{player_id}_{int(time.time() * 1000)}"
        bullet_data = {
            'id': bullet_id,
            'owner_id': player_id,
            'x': message.get('x'),
            'y': message.get('y'),
            'dx': message.get('dx'),
            'dy': message.get('dy'),
            'type': message.get('bullet_type', 'standard'),
            'created_at': time.time()
        }

        self.bullets[bullet_id] = bullet_data

        return {'success': True, 'bullet_id': bullet_id}

    def start_game(self):
        """Inicia uma nova partida."""
        self.game_state = "playing"

        # Cria bots inimigos apenas se habilitado
        if self.enable_bots:
            self.create_bots()
            print(f"Jogo iniciado! Modo: PvE com {len(self.bots)} bots")
        else:
            print(f"Jogo iniciado! Modo: PvP com {len(self.players)} jogadores")

        # Notifica todos os jogadores
        self.broadcast_game_started()

    def create_bots(self):
        """Cria bots inimigos no servidor."""
        enemy_types = create_enemy_squad(NUM_BOTS, difficulty_level=3)

        for i, enemy_type in enumerate(enemy_types):
            bot_id = f"bot_{i}"

            # Posição aleatória longe dos jogadores
            while True:
                bot_x = random.randint(100, WORLD_WIDTH - 100)
                bot_y = random.randint(100, WORLD_HEIGHT - 100)

                # Verifica se está longe de todos os jogadores
                too_close = False
                for player in self.players.values():
                    distance = ((bot_x - player['x'])**2 + (bot_y - player['y'])**2)**0.5
                    if distance < 300:
                        too_close = True
                        break

                if not too_close:
                    break

            bot_data = {
                'id': bot_id,
                'type': enemy_type.name,
                'x': bot_x,
                'y': bot_y,
                'health': enemy_type.stats['health'],
                'max_health': enemy_type.stats['health'],
                'state': 'patrol',
                'target_player': None,
                'last_shot': 0
            }

            self.bots[bot_id] = bot_data

    def game_loop(self):
        """Loop principal do jogo no servidor."""
        while True:
            current_time = time.time()

            if current_time - self.last_update >= 1.0 / self.tick_rate:
                if self.game_state == "playing":
                    self.update_game_state()
                    self.broadcast_game_state()

                self.last_update = current_time

            time.sleep(0.001)  # Pequena pausa para não sobrecarregar CPU

    def update_game_state(self):
        """Atualiza estado do jogo."""
        current_time = time.time()

        # Atualiza balas
        self.update_bullets()

        # Atualiza bots (IA básica) - só se habilitados
        if self.enable_bots:
            self.update_bots()

        # Verifica colisões
        self.check_collisions()

        # Verifica condições de fim de jogo
        self.check_game_over()

    def update_bullets(self):
        """Atualiza posição das balas."""
        bullets_to_remove = []

        for bullet_id, bullet in self.bullets.items():
            # Move bala
            bullet['x'] += bullet['dx'] * 12  # Velocidade da bala
            bullet['y'] += bullet['dy'] * 12

            # Remove balas que saíram do mapa ou são muito antigas
            if (bullet['x'] < 0 or bullet['x'] > WORLD_WIDTH or
                bullet['y'] < 0 or bullet['y'] > WORLD_HEIGHT or
                time.time() - bullet['created_at'] > 5):
                bullets_to_remove.append(bullet_id)

        for bullet_id in bullets_to_remove:
            del self.bullets[bullet_id]

    def update_bots(self):
        """Atualiza IA dos bots (versão simplificada)."""
        for bot in self.bots.values():
            if bot['health'] <= 0:
                continue

            # Encontra jogador mais próximo
            closest_player = None
            min_distance = float('inf')

            for player in self.players.values():
                if player['health'] <= 0:
                    continue

                distance = ((bot['x'] - player['x'])**2 + (bot['y'] - player['y'])**2)**0.5
                if distance < min_distance:
                    min_distance = distance
                    closest_player = player

            if closest_player and min_distance < 200:
                # Move em direção ao jogador
                dx = closest_player['x'] - bot['x']
                dy = closest_player['y'] - bot['y']

                if min_distance > 0:
                    # Normaliza direção
                    dx /= min_distance
                    dy /= min_distance

                    # Move bot
                    bot['x'] += dx * 2  # Velocidade do bot
                    bot['y'] += dy * 2

                    bot['state'] = 'chase'
                    bot['target_player'] = closest_player['id']

                    # Atira se próximo o suficiente
                    if min_distance < 150 and time.time() - bot['last_shot'] > 1.0:
                        self.bot_shoot(bot, dx, dy)
                        bot['last_shot'] = time.time()
            else:
                bot['state'] = 'patrol'
                bot['target_player'] = None

    def bot_shoot(self, bot, dx, dy):
        """Bot atira no jogador."""
        bullet_id = f"bot_bullet_{bot['id']}_{int(time.time() * 1000)}"
        bullet_data = {
            'id': bullet_id,
            'owner_id': bot['id'],
            'x': bot['x'],
            'y': bot['y'],
            'dx': dx,
            'dy': dy,
            'type': 'standard',
            'created_at': time.time()
        }

        self.bullets[bullet_id] = bullet_data

    def check_collisions(self):
        """Verifica colisões entre balas e entidades."""
        bullets_to_remove = []

        for bullet_id, bullet in self.bullets.items():
            bullet_rect = {
                'x': bullet['x'] - 2,
                'y': bullet['y'] - 2,
                'width': 4,
                'height': 4
            }

            # Colisão com jogadores
            for player in self.players.values():
                # Em PvE, só bots podem atingir jogadores
                # Em PvP, jogadores podem atingir outros jogadores
                can_hit_player = False

                if self.game_mode == "pve":
                    # PvE: Só bots atingem jogadores
                    can_hit_player = (bullet['owner_id'] != player['id'] and
                                    bullet['owner_id'].startswith('bot_') and
                                    player['health'] > 0)
                elif self.game_mode == "pvp":
                    # PvP: Jogadores podem atingir outros jogadores
                    can_hit_player = (bullet['owner_id'] != player['id'] and
                                    player['health'] > 0)

                if can_hit_player:

                    player_rect = {
                        'x': player['x'] - 20,
                        'y': player['y'] - 20,
                        'width': 40,
                        'height': 40
                    }

                    if self.rects_collide(bullet_rect, player_rect):
                        player['health'] -= 25
                        bullets_to_remove.append(bullet_id)

                        if player['health'] <= 0:
                            player['deaths'] += 1
                            print(f"Jogador {player['name']} foi eliminado!")
                        break

            # Colisão com bots (só se habilitados)
            if self.enable_bots:
                for bot in self.bots.values():
                    if (bullet['owner_id'] != bot['id'] and
                        bullet['owner_id'].startswith('bot_') == False and
                        bot['health'] > 0):

                        bot_rect = {
                            'x': bot['x'] - 20,
                            'y': bot['y'] - 20,
                            'width': 40,
                            'height': 40
                        }

                        if self.rects_collide(bullet_rect, bot_rect):
                            bot['health'] -= 25
                            bullets_to_remove.append(bullet_id)

                            if bot['health'] <= 0:
                                # Dá kill para o dono da bala
                                if bullet['owner_id'] in self.players:
                                    self.players[bullet['owner_id']]['kills'] += 1
                                print(f"Bot {bot['id']} foi eliminado!")
                            break

        for bullet_id in bullets_to_remove:
            if bullet_id in self.bullets:
                del self.bullets[bullet_id]

    def rects_collide(self, rect1, rect2):
        """Verifica colisão entre dois retângulos."""
        return (rect1['x'] < rect2['x'] + rect2['width'] and
                rect1['x'] + rect1['width'] > rect2['x'] and
                rect1['y'] < rect2['y'] + rect2['height'] and
                rect1['y'] + rect1['height'] > rect2['y'])

    def check_game_over(self):
        """Verifica condições de fim de jogo."""
        alive_players = [p for p in self.players.values() if p['health'] > 0]

        if self.game_mode == "pve":
            if self.enable_bots:
                # PvE com bots: Jogadores vs Bots
                alive_bots = [b for b in self.bots.values() if b['health'] > 0]

                if len(alive_bots) == 0:
                    self.game_state = "game_over"
                    self.broadcast_game_over("victory")
                elif len(alive_players) == 0:
                    self.game_state = "game_over"
                    self.broadcast_game_over("defeat")
            else:
                # PvE sem bots: Modo exploração cooperativa
                # Não há condição de fim de jogo automática
                # Jogadores podem sair quando quiserem
                if len(alive_players) == 0:
                    self.game_state = "game_over"
                    self.broadcast_game_over("all_disconnected")

        elif self.game_mode == "pvp":
            # Modo PvP: Último jogador vivo vence
            if len(alive_players) <= 1:
                self.game_state = "game_over"
                winner = alive_players[0] if alive_players else None
                self.broadcast_game_over("pvp_end", winner)

    def get_full_game_state(self):
        """Retorna estado completo do jogo."""
        return {
            'success': True,
            'game_state': self.game_state,
            'players': self.players,
            'bots': self.bots,
            'bullets': self.bullets,
            'timestamp': time.time()
        }

    def broadcast_game_state(self):
        """Envia estado do jogo para todos os clientes (implementação futura)."""
        pass

    def broadcast_game_started(self):
        """Notifica que o jogo começou (implementação futura)."""
        pass

    def broadcast_game_over(self, result):
        """Notifica fim de jogo (implementação futura)."""
        pass

    def broadcast_player_left(self, player_id):
        """Notifica que um jogador saiu (implementação futura)."""
        pass

if __name__ == "__main__":
    server = GameServer()
    try:
        server.start()
    except KeyboardInterrupt:
        print("\nServidor encerrado pelo usuário")
    except Exception as e:
        print(f"Erro fatal no servidor: {e}")
