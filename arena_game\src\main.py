# src/main.py

import pygame
import sys
import os
import random
import time
from settings import (WIDTH, HEIGHT, FPS, WHITE, WORLD_WIDTH, WORLD_HEIGHT, NUM_BOTS,
                     PLAYER_DAMAGE, BOT_DAMAGE)
from entities.player import Player
from entities.bot import Bo<PERSON>
from camera import Camera
from world import World
from audio import audio_manager
from ui import ui
from menu import menu, GameState
from character_select import character_select
from characters import get_character
from enemy_types import create_enemy_squad

# Variável global para contar bots derrotados
bots_defeated = 0

def create_player(character_id=None):
    """Cria um novo jogador com personagem opcional."""
    character = get_character(character_id) if character_id else None

    return Player(
        x=WORLD_WIDTH // 2,
        y=WORLD_HEIGHT // 2,
        sprite_path=os.path.join("assets", "player1.png"),
        controls={
            'up': pygame.K_w,
            'down': pygame.K_s,
            'left': pygame.K_a,
            'right': pygame.K_d,
            'shoot': pygame.K_SPACE
        },
        character=character
    )

def create_bots(player):
    """Cria os bots inimigos modernos e variados."""
    bots = []

    # Cria esquadrão balanceado de inimigos
    enemy_types = create_enemy_squad(NUM_BOTS, difficulty_level=3)

    # Primeira passada: cria todos os bots com tipos específicos
    for i, enemy_type in enumerate(enemy_types):
        # Posiciona bots aleatoriamente no mundo
        bot_x = random.randint(100, WORLD_WIDTH - 100)
        bot_y = random.randint(100, WORLD_HEIGHT - 100)

        # Evita spawnar muito perto do jogador
        while abs(bot_x - player.rect.x) < 300 and abs(bot_y - player.rect.y) < 300:
            bot_x = random.randint(100, WORLD_WIDTH - 100)
            bot_y = random.randint(100, WORLD_HEIGHT - 100)

        bot = Bot(
            x=bot_x,
            y=bot_y,
            sprite_path=os.path.join("assets", "player2.png"),
            target=player,
            bot_list=bots,
            enemy_type=enemy_type
        )
        bots.append(bot)

    # Segunda passada: atualiza a lista de bots em cada bot para coordenação
    for bot in bots:
        bot.bot_list = bots

    return bots

def main():
    pygame.init()
    screen = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("Arena Battle Royale - Edição Completa")
    clock = pygame.time.Clock()

    # Inicialização dos sistemas
    world = World()
    camera = Camera()

    # Estado do jogo
    game_state = GameState.MENU
    player = None
    bots = []
    bots_defeated = 0
    game_start_time = 0

    # Loop principal do jogo
    running = True

    while running:
        clock.tick(FPS)
        current_time = pygame.time.get_ticks()

        # Captura eventos
        events = pygame.event.get()
        for event in events:
            if event.type == pygame.QUIT:
                running = False

        # Gerenciamento de estados
        if game_state == GameState.MENU:
            result = menu.handle_menu_events(events)
            if result == GameState.CHARACTER_SELECT:
                game_state = GameState.CHARACTER_SELECT
            elif result == "quit":
                running = False

            # Desenha menu
            menu.draw_menu(screen)

        elif game_state == GameState.CHARACTER_SELECT:
            result = character_select.handle_events(events)
            if result == "back":
                game_state = GameState.MENU
            elif result:  # Personagem selecionado
                # Inicia novo jogo com personagem escolhido
                menu.selected_character = result
                player = create_player(result)
                bots = create_bots(player)
                bots_defeated = 0
                game_start_time = current_time
                game_state = GameState.PLAYING

            # Desenha seleção de personagem
            character_select.draw(screen)

        elif game_state == GameState.PLAYING:
            # Lógica do jogo aqui
            game_state = run_game_loop(screen, player, bots, camera, world, current_time, events)

        elif game_state == GameState.GAME_OVER:
            # Calcula estatísticas
            time_played = format_time((current_time - game_start_time) // 1000)
            stats = {
                'victory': bots_defeated >= NUM_BOTS,
                'bots_killed': bots_defeated,
                'total_bots': NUM_BOTS,
                'time_played': time_played,
                'accuracy': player.get_accuracy() if player else 0,
                'health_remaining': int((player.health / player.max_health) * 100) if player and player.is_alive() else 0
            }

            result = menu.handle_game_over_events(events, stats)
            if result == "restart":
                game_state = GameState.MENU
                menu.player_name = ""  # Reset nome para permitir mudança
            elif result == "quit":
                running = False

            # Desenha tela de game over
            menu.draw_game_over(screen, stats)

        pygame.display.flip()

    # Cleanup
    audio_manager.stop_all_sounds()
    pygame.quit()
    sys.exit()

def format_time(seconds):
    """Formata tempo em minutos:segundos."""
    minutes = seconds // 60
    seconds = seconds % 60
    return f"{minutes:02d}:{seconds:02d}"

def run_game_loop(screen, player, bots, camera, world, current_time, events):
    """
    Executa um frame do loop principal do jogo.
    Retorna o próximo estado do jogo.
    """
    # Variável global para contar bots derrotados
    global bots_defeated

    # Processa eventos específicos do jogo
    for event in events:
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_m:  # Tecla M para mutar/desmutar som
                sound_enabled = audio_manager.toggle_sound()
                print(f"Som {'ligado' if sound_enabled else 'desligado'}")
            elif event.key == pygame.K_ESCAPE:  # ESC para pausar
                return GameState.PAUSED
        else:
            # Passa eventos para o gerenciador de áudio
            audio_manager.handle_sound_timer_event(event)

    # Captura estado atual do teclado
    keys = pygame.key.get_pressed()

    # Movimentação e disparo do jogador
    player.handle_movement(keys, world)
    player.handle_shooting(keys, current_time, audio_manager)
    player.update_bullets()

    # Atualiza bots
    for bot in bots:
        if bot.health > 0:
            bot.update(current_time)

    # Colisões: balas do jogador atingindo bots
    for bullet in player.bullets[:]:
        for bot in bots:
            if bot.health > 0 and bullet.rect.colliderect(bot.rect):
                player.bullets.remove(bullet)
                player.shots_hit += 1  # Incrementa acertos
                damage = player.base_damage if hasattr(player, 'base_damage') else PLAYER_DAMAGE
                if bot.take_damage(damage):
                    bots_defeated += 1
                break

    # Colisões: balas dos bots atingindo o jogador
    for bot in bots:
        if bot.health > 0:
            for bullet in bot.bullets[:]:
                if bullet.rect.colliderect(player.rect):
                    bot.bullets.remove(bullet)
                    damage = bot.base_damage if hasattr(bot, 'base_damage') else BOT_DAMAGE
                    if player.take_damage(damage):
                        return GameState.GAME_OVER

    # Verifica condição de vitória
    if bots_defeated >= NUM_BOTS:
        return GameState.GAME_OVER

    # Atualiza sistemas
    camera.update(player)
    audio_manager.update_scheduled_stops()

    # Renderização
    world.draw(screen, camera)
    player.draw(screen, camera)

    # Desenha bots vivos
    for bot in bots:
        if bot.health > 0:
            bot.draw(screen, camera)

    # Interface do usuário
    ui.draw_game_hud(screen, player, bots, audio_manager)
    ui.draw_minimap_enhanced(screen, camera, player, bots, world)

    return GameState.PLAYING


if __name__ == "__main__":
    main()
