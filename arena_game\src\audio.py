# src/audio.py

import pygame
import os
from settings import ENABLE_SOUND, SOUND_VOLUME, SHOOT_SOUND_FILE, SHOOT_SOUND_DURATION

class AudioManager:
    def __init__(self):
        """
        Inicializa o sistema de áudio do jogo.
        """
        self.sounds = {}
        self.enabled = ENABLE_SOUND
        self.active_shoot_channels = []  # Lista de canais ativos para sons de tiro
        self.max_simultaneous_sounds = 3  # Máximo de sons simultâneos

        if self.enabled:
            try:
                # Inicializa o mixer do pygame
                pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
                pygame.mixer.init()

                # Carrega os sons
                self._load_sounds()

                print("Sistema de áudio inicializado com sucesso!")

            except pygame.error as e:
                print(f"Erro ao inicializar áudio: {e}")
                self.enabled = False
        else:
            print("Sistema de áudio desabilitado.")

    def _load_sounds(self):
        """
        Carrega todos os arquivos de som.
        """
        sounds_dir = os.path.join("..", "sounds")

        # Carrega som do tiro
        shoot_sound_path = os.path.join(sounds_dir, SHOOT_SOUND_FILE)

        try:
            if os.path.exists(shoot_sound_path):
                # Carrega o som original (sem processamento de duração por enquanto)
                self.sounds['shoot'] = pygame.mixer.Sound(shoot_sound_path)
                self.sounds['shoot'].set_volume(SOUND_VOLUME)

                # Calcula duração aproximada do arquivo
                sound_length = self.sounds['shoot'].get_length()
                print(f"Som do tiro carregado: {SHOOT_SOUND_FILE} (duração original: {sound_length:.1f}s)")

                # Nota: O som será interrompido automaticamente após SHOOT_SOUND_DURATION
                # através do sistema de cooldown e parada de canal

            else:
                print(f"Arquivo de som não encontrado: {shoot_sound_path}")

        except pygame.error as e:
            print(f"Erro ao carregar som do tiro: {e}")

    def play_shoot_sound(self):
        """
        Toca o som do tiro INSTANTANEAMENTE sem qualquer delay.
        """
        if not self.enabled or 'shoot' not in self.sounds:
            return

        try:
            # Limpa canais que já terminaram automaticamente
            self.active_shoot_channels = [ch for ch in self.active_shoot_channels if ch.get_busy()]

            # Se há muitos sons tocando, para o mais antigo
            if len(self.active_shoot_channels) >= self.max_simultaneous_sounds:
                oldest_channel = self.active_shoot_channels.pop(0)
                oldest_channel.stop()

            # TOCA O SOM IMEDIATAMENTE - SEM VERIFICAÇÕES DE TEMPO
            new_channel = self.sounds['shoot'].play()
            if new_channel:
                self.active_shoot_channels.append(new_channel)

                # Agenda parada automática após 2 segundos
                self._schedule_sound_stop(new_channel, SHOOT_SOUND_DURATION)

        except pygame.error as e:
            print(f"Erro ao tocar som do tiro: {e}")

    def _schedule_sound_stop(self, channel, duration):
        """
        Agenda a parada de um canal após uma duração específica.
        """
        # Cria um evento customizado para parar este canal específico
        stop_time = pygame.time.get_ticks() + int(duration * 1000)
        # Armazena o canal e tempo de parada para verificação posterior
        if not hasattr(self, 'scheduled_stops'):
            self.scheduled_stops = []
        self.scheduled_stops.append((channel, stop_time))

    def update_scheduled_stops(self):
        """
        Verifica e executa paradas agendadas de sons.
        Deve ser chamado no loop principal do jogo.
        """
        if not hasattr(self, 'scheduled_stops'):
            return

        current_time = pygame.time.get_ticks()
        remaining_stops = []

        for channel, stop_time in self.scheduled_stops:
            if current_time >= stop_time:
                # Hora de parar este canal
                if channel.get_busy():
                    channel.stop()
            else:
                # Ainda não é hora, mantém na lista
                remaining_stops.append((channel, stop_time))

        self.scheduled_stops = remaining_stops

    def handle_sound_timer_event(self, event):
        """
        Manipula eventos de timer (mantido para compatibilidade).
        """
        # Não usado mais, mas mantido para não quebrar o código existente
        _ = event  # Evita warning de variável não usada

    def set_volume(self, volume):
        """
        Ajusta o volume de todos os sons.
        volume: float entre 0.0 e 1.0
        """
        if self.enabled:
            for sound in self.sounds.values():
                sound.set_volume(volume)

    def toggle_sound(self):
        """
        Liga/desliga o som.
        """
        self.enabled = not self.enabled
        if not self.enabled:
            pygame.mixer.stop()
        return self.enabled

    def stop_all_sounds(self):
        """
        Para todos os sons que estão tocando.
        """
        if self.enabled:
            # Para todos os canais de tiro ativos
            for channel in self.active_shoot_channels:
                if channel.get_busy():
                    channel.stop()
            self.active_shoot_channels.clear()

            # Limpa paradas agendadas
            if hasattr(self, 'scheduled_stops'):
                self.scheduled_stops.clear()

            # Para todos os outros sons
            pygame.mixer.stop()

# Instância global do gerenciador de áudio
audio_manager = AudioManager()
