# src/ui.py

import pygame
from settings import (WIDTH, HEIGHT, UI_FONT_SIZE, UI_SMALL_FONT_SIZE, UI_LARGE_FONT_SIZE, 
                     UI_TITLE_FONT_SIZE, UI_GREEN, UI_RED, UI_YELLOW, UI_ORANGE, 
                     UI_DARK_GRAY, UI_LIGHT_GRAY, UI_BLUE, WHITE, BLACK, GRAY)

class UI:
    def __init__(self):
        """
        Inicializa o sistema de interface do usuário.
        """
        pygame.font.init()
        
        # Carrega fontes
        self.font_small = pygame.font.SysFont("arial", UI_SMALL_FONT_SIZE, bold=True)
        self.font_normal = pygame.font.SysFont("arial", UI_FONT_SIZE, bold=True)
        self.font_large = pygame.font.SysFont("arial", UI_LARGE_FONT_SIZE, bold=True)
        self.font_title = pygame.font.SysFont("arial", UI_TITLE_FONT_SIZE, bold=True)
        
    def draw_text(self, surface, text, font, color, x, y, center=False):
        """
        Desenha texto na tela com opção de centralização.
        """
        text_surface = font.render(str(text), True, color)
        if center:
            text_rect = text_surface.get_rect(center=(x, y))
            surface.blit(text_surface, text_rect)
        else:
            surface.blit(text_surface, (x, y))
        return text_surface.get_rect()
    
    def draw_health_bar(self, surface, x, y, width, height, current_health, max_health):
        """
        Desenha uma barra de vida com porcentagem.
        """
        # Calcula porcentagem
        health_percent = max(0, current_health / max_health)
        
        # Cor da barra baseada na vida
        if health_percent > 0.6:
            bar_color = UI_GREEN
        elif health_percent > 0.3:
            bar_color = UI_YELLOW
        else:
            bar_color = UI_RED
        
        # Desenha fundo da barra
        pygame.draw.rect(surface, UI_DARK_GRAY, (x, y, width, height))
        pygame.draw.rect(surface, WHITE, (x, y, width, height), 2)
        
        # Desenha barra de vida
        if health_percent > 0:
            fill_width = int(width * health_percent)
            pygame.draw.rect(surface, bar_color, (x + 2, y + 2, fill_width - 4, height - 4))
        
        # Texto da porcentagem
        health_text = f"{int(current_health)}/{int(max_health)} ({int(health_percent * 100)}%)"
        text_rect = self.draw_text(surface, health_text, self.font_small, WHITE, 
                                 x + width // 2, y + height // 2, center=True)
        
    def draw_ammo_counter(self, surface, x, y, current_ammo, max_ammo):
        """
        Desenha contador de munição estilizado.
        """
        # Cor baseada na munição
        ammo_percent = current_ammo / max_ammo
        if ammo_percent > 0.5:
            ammo_color = UI_GREEN
        elif ammo_percent > 0.2:
            ammo_color = UI_YELLOW
        else:
            ammo_color = UI_RED
        
        # Desenha ícone de bala
        bullet_rect = pygame.Rect(x, y, 8, 16)
        pygame.draw.ellipse(surface, ammo_color, bullet_rect)
        pygame.draw.rect(surface, UI_DARK_GRAY, bullet_rect, 1)
        
        # Texto da munição
        ammo_text = f"{current_ammo}/{max_ammo}"
        self.draw_text(surface, ammo_text, self.font_normal, ammo_color, x + 20, y)
        
    def draw_minimap_enhanced(self, surface, camera, player, bots, world):
        """
        Desenha um minimapa melhorado com bordas e estilo.
        """
        minimap_size = 150
        minimap_x = WIDTH - minimap_size - 20
        minimap_y = 20
        
        # Fundo do minimapa com bordas
        minimap_rect = pygame.Rect(minimap_x - 5, minimap_y - 5, minimap_size + 10, minimap_size + 10)
        pygame.draw.rect(surface, UI_DARK_GRAY, minimap_rect)
        pygame.draw.rect(surface, WHITE, minimap_rect, 3)
        
        inner_rect = pygame.Rect(minimap_x, minimap_y, minimap_size, minimap_size)
        pygame.draw.rect(surface, (30, 30, 30), inner_rect)
        
        # Escala do minimapa
        scale_x = minimap_size / world.width
        scale_y = minimap_size / world.height
        
        # Desenha o jogador no minimapa (maior e com borda)
        player_x = minimap_x + int(player.rect.centerx * scale_x)
        player_y = minimap_y + int(player.rect.centery * scale_y)
        pygame.draw.circle(surface, WHITE, (player_x, player_y), 5)
        pygame.draw.circle(surface, UI_GREEN, (player_x, player_y), 3)
        
        # Desenha os bots no minimapa
        for bot in bots:
            if bot.health > 0:
                bot_x = minimap_x + int(bot.rect.centerx * scale_x)
                bot_y = minimap_y + int(bot.rect.centery * scale_y)
                
                # Bots elite são maiores e dourados
                if bot.is_elite:
                    pygame.draw.circle(surface, UI_YELLOW, (bot_x, bot_y), 4)
                    pygame.draw.circle(surface, UI_RED, (bot_x, bot_y), 2)
                else:
                    pygame.draw.circle(surface, UI_RED, (bot_x, bot_y), 3)
        
        # Desenha a área visível da câmera
        cam_x = minimap_x + int(camera.x * scale_x)
        cam_y = minimap_y + int(camera.y * scale_y)
        cam_w = int(WIDTH * scale_x)
        cam_h = int(HEIGHT * scale_y)
        pygame.draw.rect(surface, UI_BLUE, (cam_x, cam_y, cam_w, cam_h), 2)
        
        # Título do minimapa
        self.draw_text(surface, "MAPA", self.font_small, WHITE, 
                      minimap_x + minimap_size // 2, minimap_y - 20, center=True)
    
    def draw_game_hud(self, surface, player, bots, audio_manager):
        """
        Desenha o HUD principal do jogo.
        """
        # Painel de fundo para o HUD
        hud_rect = pygame.Rect(10, 10, 300, 120)
        pygame.draw.rect(surface, (0, 0, 0, 128), hud_rect)
        pygame.draw.rect(surface, WHITE, hud_rect, 2)
        
        # Barra de vida
        self.draw_text(surface, "VIDA:", self.font_small, WHITE, 20, 20)
        self.draw_health_bar(surface, 20, 35, 200, 25, player.health, player.max_health)
        
        # Contador de munição
        self.draw_text(surface, "MUNIÇÃO:", self.font_small, WHITE, 20, 70)
        self.draw_ammo_counter(surface, 20, 85, player.current_ammo, player.max_ammo)
        
        # Contador de inimigos
        bots_alive = sum(1 for bot in bots if bot.health > 0)
        elite_bots_alive = sum(1 for bot in bots if bot.health > 0 and bot.is_elite)
        
        enemies_text = f"INIMIGOS: {bots_alive}/{len(bots)}"
        self.draw_text(surface, enemies_text, self.font_small, UI_YELLOW, 20, 110)
        
        if elite_bots_alive > 0:
            elite_text = f"ELITES: {elite_bots_alive}"
            self.draw_text(surface, elite_text, self.font_small, UI_RED, 150, 110)
        
        # Status do som
        sound_status = "🔊" if audio_manager.enabled else "🔇"
        self.draw_text(surface, f"SOM: {sound_status}", self.font_small, WHITE, 
                      WIDTH - 120, HEIGHT - 30)
        
        # Coordenadas (debug)
        coords_text = f"POS: ({int(player.rect.x)}, {int(player.rect.y)})"
        self.draw_text(surface, coords_text, self.font_small, UI_LIGHT_GRAY, 
                      10, HEIGHT - 30)
    
    def draw_button(self, surface, text, x, y, width, height, color, text_color, border_color=WHITE):
        """
        Desenha um botão estilizado.
        """
        button_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(surface, color, button_rect)
        pygame.draw.rect(surface, border_color, button_rect, 3)
        
        # Texto centralizado
        self.draw_text(surface, text, self.font_normal, text_color, 
                      x + width // 2, y + height // 2, center=True)
        
        return button_rect
    
    def draw_input_box(self, surface, text, x, y, width, height, active=False):
        """
        Desenha uma caixa de input para texto.
        """
        box_color = UI_LIGHT_GRAY if active else UI_DARK_GRAY
        border_color = UI_BLUE if active else WHITE
        
        input_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(surface, box_color, input_rect)
        pygame.draw.rect(surface, border_color, input_rect, 3)
        
        # Texto
        if text:
            self.draw_text(surface, text, self.font_normal, BLACK, x + 10, y + height // 2 - 10)
        
        # Cursor piscando se ativo
        if active:
            cursor_x = x + 10 + self.font_normal.size(text)[0] if text else x + 10
            pygame.draw.line(surface, BLACK, (cursor_x, y + 5), (cursor_x, y + height - 5), 2)
        
        return input_rect

# Instância global da UI
ui = UI()
