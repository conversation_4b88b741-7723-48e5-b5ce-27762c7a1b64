# src/modern_graphics.py

import pygame
import math
import random
from settings import PLAYER_WIDTH, PLAYER_HEIGHT

class ModernSprite:
    def __init__(self, character_type, primary_color, secondary_color, accent_color):
        """
        Cria sprites modernos e realistas para os personagens.
        """
        self.character_type = character_type
        self.primary_color = primary_color
        self.secondary_color = secondary_color
        self.accent_color = accent_color
        self.size = (PLAYER_WIDTH, PLAYER_HEIGHT)
        
        # Cria o sprite principal
        self.sprite = self.create_modern_sprite()
        
        # Cria variações para animação
        self.idle_frames = self.create_idle_animation()
        self.walk_frames = self.create_walk_animation()
        
    def create_modern_sprite(self):
        """
        Cria um sprite moderno com efeitos 2.5D.
        """
        sprite = pygame.Surface(self.size, pygame.SRCALPHA)
        
        if self.character_type == "tactical_operator":
            return self.create_tactical_operator(sprite)
        elif self.character_type == "cyber_soldier":
            return self.create_cyber_soldier(sprite)
        elif self.character_type == "heavy_assault":
            return self.create_heavy_assault(sprite)
        elif self.character_type == "stealth_agent":
            return self.create_stealth_agent(sprite)
        elif self.character_type == "combat_medic":
            return self.create_combat_medic(sprite)
        elif self.character_type == "demolition_expert":
            return self.create_demolition_expert(sprite)
        
        return sprite
    
    def create_tactical_operator(self, sprite):
        """Operador Tático - Moderno e equilibrado."""
        # Corpo principal (uniforme tático)
        body_rect = pygame.Rect(8, 12, 24, 20)
        pygame.draw.ellipse(sprite, self.primary_color, body_rect)
        
        # Colete tático
        vest_rect = pygame.Rect(10, 14, 20, 16)
        pygame.draw.rect(sprite, self.secondary_color, vest_rect)
        
        # Cabeça com capacete
        head_rect = pygame.Rect(14, 4, 12, 12)
        pygame.draw.circle(sprite, (220, 180, 140), (20, 10), 6)  # Pele
        pygame.draw.arc(sprite, self.accent_color, head_rect, 0, math.pi, 3)  # Capacete
        
        # Equipamentos
        # Óculos tático
        pygame.draw.rect(sprite, (50, 50, 50), (16, 8, 8, 3))
        
        # Pernas (calças táticas)
        pygame.draw.rect(sprite, self.primary_color, (12, 32, 6, 8))
        pygame.draw.rect(sprite, self.primary_color, (22, 32, 6, 8))
        
        # Botas
        pygame.draw.rect(sprite, (40, 40, 40), (11, 38, 8, 4))
        pygame.draw.rect(sprite, (40, 40, 40), (21, 38, 8, 4))
        
        # Arma (rifle de assalto)
        weapon_rect = pygame.Rect(30, 18, 8, 3)
        pygame.draw.rect(sprite, (60, 60, 60), weapon_rect)
        
        # Detalhes luminosos (LED no capacete)
        pygame.draw.circle(sprite, (0, 255, 100), (18, 6), 1)
        
        return sprite
    
    def create_cyber_soldier(self, sprite):
        """Soldado Cibernético - Futurista com implantes."""
        # Corpo (exoesqueleto)
        body_rect = pygame.Rect(8, 12, 24, 20)
        pygame.draw.ellipse(sprite, self.primary_color, body_rect)
        
        # Implantes cibernéticos
        pygame.draw.rect(sprite, self.secondary_color, (6, 16, 4, 12))  # Braço cibernético
        pygame.draw.rect(sprite, self.secondary_color, (30, 16, 4, 12))  # Outro braço
        
        # Cabeça com visor
        pygame.draw.circle(sprite, (200, 200, 200), (20, 10), 6)  # Cabeça metálica
        pygame.draw.rect(sprite, self.accent_color, (16, 8, 8, 4))  # Visor
        
        # Pernas cibernéticas
        pygame.draw.rect(sprite, self.secondary_color, (12, 32, 6, 8))
        pygame.draw.rect(sprite, self.secondary_color, (22, 32, 6, 8))
        
        # Efeitos de energia
        for i in range(3):
            x = 8 + i * 8
            pygame.draw.circle(sprite, self.accent_color, (x, 20), 1)
        
        # Arma de energia
        weapon_rect = pygame.Rect(30, 16, 10, 4)
        pygame.draw.rect(sprite, self.accent_color, weapon_rect)
        
        return sprite
    
    def create_heavy_assault(self, sprite):
        """Soldado Pesado - Tanque com armadura."""
        # Corpo massivo
        body_rect = pygame.Rect(6, 10, 28, 24)
        pygame.draw.ellipse(sprite, self.primary_color, body_rect)
        
        # Armadura pesada
        armor_rect = pygame.Rect(8, 12, 24, 20)
        pygame.draw.rect(sprite, self.secondary_color, armor_rect)
        
        # Capacete pesado
        helmet_rect = pygame.Rect(12, 2, 16, 14)
        pygame.draw.ellipse(sprite, self.accent_color, helmet_rect)
        
        # Visor
        pygame.draw.rect(sprite, (100, 150, 255), (16, 6, 8, 4))
        
        # Pernas robustas
        pygame.draw.rect(sprite, self.primary_color, (10, 34, 8, 6))
        pygame.draw.rect(sprite, self.primary_color, (22, 34, 8, 6))
        
        # Arma pesada (minigun)
        weapon_rect = pygame.Rect(32, 14, 6, 8)
        pygame.draw.rect(sprite, (80, 80, 80), weapon_rect)
        
        # Detalhes da armadura
        pygame.draw.rect(sprite, self.accent_color, (14, 16, 12, 2))
        pygame.draw.rect(sprite, self.accent_color, (14, 24, 12, 2))
        
        return sprite
    
    def create_stealth_agent(self, sprite):
        """Agente Furtivo - Elegante e ágil."""
        # Corpo esguio
        body_rect = pygame.Rect(10, 14, 20, 18)
        pygame.draw.ellipse(sprite, self.primary_color, body_rect)
        
        # Cabeça com máscara
        pygame.draw.circle(sprite, (180, 160, 140), (20, 10), 5)  # Pele
        pygame.draw.polygon(sprite, self.secondary_color, [(16, 12), (24, 12), (20, 8)])  # Máscara
        
        # Óculos de visão noturna
        pygame.draw.circle(sprite, (100, 255, 100), (18, 9), 2)
        pygame.draw.circle(sprite, (100, 255, 100), (22, 9), 2)
        
        # Pernas ágeis
        pygame.draw.rect(sprite, self.primary_color, (13, 32, 5, 8))
        pygame.draw.rect(sprite, self.primary_color, (22, 32, 5, 8))
        
        # Arma silenciada
        weapon_rect = pygame.Rect(28, 18, 10, 2)
        pygame.draw.rect(sprite, (60, 60, 60), weapon_rect)
        
        # Efeito de camuflagem (pontos semi-transparentes)
        for i in range(5):
            x = random.randint(8, 32)
            y = random.randint(12, 30)
            color = (*self.accent_color, 128)
            pygame.draw.circle(sprite, color[:3], (x, y), 1)
        
        return sprite
    
    def create_combat_medic(self, sprite):
        """Médico de Combate - Suporte tático."""
        # Corpo (uniforme médico)
        body_rect = pygame.Rect(9, 13, 22, 19)
        pygame.draw.ellipse(sprite, self.primary_color, body_rect)
        
        # Cruz médica
        pygame.draw.rect(sprite, (255, 50, 50), (18, 16, 4, 8))
        pygame.draw.rect(sprite, (255, 50, 50), (16, 18, 8, 4))
        
        # Cabeça com boné médico
        pygame.draw.circle(sprite, (220, 180, 140), (20, 10), 5)
        pygame.draw.rect(sprite, self.secondary_color, (16, 4, 8, 4))
        
        # Kit médico nas costas
        pygame.draw.rect(sprite, self.accent_color, (6, 14, 4, 10))
        
        # Pernas
        pygame.draw.rect(sprite, self.primary_color, (13, 32, 6, 8))
        pygame.draw.rect(sprite, self.primary_color, (21, 32, 6, 8))
        
        # Arma de defesa
        weapon_rect = pygame.Rect(30, 20, 6, 2)
        pygame.draw.rect(sprite, (70, 70, 70), weapon_rect)
        
        return sprite
    
    def create_demolition_expert(self, sprite):
        """Especialista em Demolição - Explosivos e destruição."""
        # Corpo robusto
        body_rect = pygame.Rect(8, 12, 24, 20)
        pygame.draw.ellipse(sprite, self.primary_color, body_rect)
        
        # Colete de explosivos
        vest_rect = pygame.Rect(10, 14, 20, 16)
        pygame.draw.rect(sprite, self.secondary_color, vest_rect)
        
        # Granadas no colete
        for i in range(3):
            x = 12 + i * 6
            pygame.draw.circle(sprite, (100, 100, 100), (x, 18), 2)
            pygame.draw.circle(sprite, self.accent_color, (x, 18), 1)
        
        # Cabeça com óculos de proteção
        pygame.draw.circle(sprite, (200, 170, 130), (20, 10), 5)
        pygame.draw.circle(sprite, (255, 200, 0), (18, 9), 3)
        pygame.draw.circle(sprite, (255, 200, 0), (22, 9), 3)
        
        # Pernas
        pygame.draw.rect(sprite, self.primary_color, (12, 32, 6, 8))
        pygame.draw.rect(sprite, self.primary_color, (22, 32, 6, 8))
        
        # Lança-granadas
        weapon_rect = pygame.Rect(30, 16, 8, 6)
        pygame.draw.rect(sprite, (80, 80, 80), weapon_rect)
        
        return sprite
    
    def create_idle_animation(self):
        """Cria frames de animação idle."""
        frames = []
        base_sprite = self.sprite.copy()
        
        for i in range(4):
            frame = base_sprite.copy()
            # Adiciona leve movimento de respiração
            offset = int(math.sin(i * math.pi / 2) * 1)
            if offset != 0:
                frame = pygame.transform.scale(frame, (self.size[0], self.size[1] + offset))
            frames.append(frame)
        
        return frames
    
    def create_walk_animation(self):
        """Cria frames de animação de caminhada."""
        frames = []
        base_sprite = self.sprite.copy()
        
        for i in range(4):
            frame = base_sprite.copy()
            # Adiciona leve inclinação para simular caminhada
            angle = math.sin(i * math.pi / 2) * 2
            if angle != 0:
                frame = pygame.transform.rotate(frame, angle)
            frames.append(frame)
        
        return frames

def create_modern_character_sprite(character_type, primary_color, secondary_color, accent_color):
    """
    Função helper para criar sprites modernos.
    """
    modern_sprite = ModernSprite(character_type, primary_color, secondary_color, accent_color)
    return modern_sprite.sprite
