# src/utils.py

import pygame

def draw_text(surface, text, size, x, y, color=(0, 0, 0)):
    """
    Desenha texto na tela usando uma fonte padrão.
    surface: superfície do Pygame onde renderizar o texto
    text: string a ser desenhada
    size: tamanho da fonte
    x, y: coordenadas (pixels) de topo-esquerda para desenhar o texto
    color: cor do texto em RGB (padrão: preto)
    """
    # Ensure pygame font is initialized
    if not pygame.get_init():
        pygame.init()
    if not pygame.font.get_init():
        pygame.font.init()

    font = pygame.font.SysFont("comicsansms", size, bold=True)
    render = font.render(text, True, color)
    surface.blit(render, (x, y))

def draw_minimap(surface, camera, player, bots, world):
    """
    Desenha um minimapa no canto superior direito da tela.
    """
    minimap_size = 150
    minimap_x = surface.get_width() - minimap_size - 10
    minimap_y = 10

    # Fundo do minimapa
    minimap_rect = pygame.Rect(minimap_x, minimap_y, minimap_size, minimap_size)
    pygame.draw.rect(surface, (50, 50, 50), minimap_rect)
    pygame.draw.rect(surface, (255, 255, 255), minimap_rect, 2)

    # Escala do minimapa
    scale_x = minimap_size / world.width
    scale_y = minimap_size / world.height

    # Desenha o jogador no minimapa
    player_x = minimap_x + int(player.rect.centerx * scale_x)
    player_y = minimap_y + int(player.rect.centery * scale_y)
    pygame.draw.circle(surface, (0, 255, 0), (player_x, player_y), 3)

    # Desenha os bots no minimapa
    for bot in bots:
        if bot.life > 0:
            bot_x = minimap_x + int(bot.rect.centerx * scale_x)
            bot_y = minimap_y + int(bot.rect.centery * scale_y)

            # Bots elite são maiores e mais escuros
            if bot.is_elite:
                pygame.draw.circle(surface, (150, 0, 0), (bot_x, bot_y), 4)
                pygame.draw.circle(surface, (255, 255, 0), (bot_x, bot_y), 2)  # Centro dourado
            else:
                pygame.draw.circle(surface, (255, 0, 0), (bot_x, bot_y), 2)

    # Desenha a área visível da câmera
    cam_x = minimap_x + int(camera.x * scale_x)
    cam_y = minimap_y + int(camera.y * scale_y)
    cam_w = int(surface.get_width() * scale_x)
    cam_h = int(surface.get_height() * scale_y)
    pygame.draw.rect(surface, (255, 255, 0), (cam_x, cam_y, cam_w, cam_h), 1)
