# src/utils.py

import pygame

def draw_text(surface, text, size, x, y, color=(0, 0, 0)):
    """
    Desenha texto na tela usando uma fonte padrão.
    surface: superfície do Pygame onde renderizar o texto
    text: string a ser desenhada
    size: tamanho da fonte
    x, y: coordenadas (pixels) de topo-esquerda para desenhar o texto
    color: cor do texto em RGB (padrão: preto)
    """
    font = pygame.font.SysFont("comicsansms", size, bold=True)
    render = font.render(text, True, color)
    surface.blit(render, (x, y))
