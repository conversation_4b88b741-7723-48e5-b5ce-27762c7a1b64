# src/characters.py

import pygame
from settings import PLAYER_WIDTH, PLAYER_HEIGHT

class Character:
    def __init__(self, name, description, primary_color, secondary_color, accent_color, stats):
        """
        Classe base para personagens do jogo.
        """
        self.name = name
        self.description = description
        self.primary_color = primary_color
        self.secondary_color = secondary_color
        self.accent_color = accent_color
        self.stats = stats
        
        # Cria sprite baseado nas cores
        self.sprite = self.create_sprite()
    
    def create_sprite(self):
        """
        Cria um sprite simples baseado nas cores do personagem.
        """
        sprite = pygame.Surface((PLAYER_WIDTH, PLAYER_HEIGHT))
        sprite.fill(self.primary_color)
        
        # Adiciona detalhes com cores secundárias
        # Cabeça
        pygame.draw.circle(sprite, self.secondary_color, (20, 10), 8)
        
        # Detalhes do corpo
        pygame.draw.rect(sprite, self.accent_color, (15, 15, 10, 5))  # Ombros
        pygame.draw.rect(sprite, self.accent_color, (5, 25, 30, 3))   # Cinto
        
        # Pernas
        pygame.draw.rect(sprite, self.secondary_color, (12, 30, 6, 10))  # Perna esquerda
        pygame.draw.rect(sprite, self.secondary_color, (22, 30, 6, 10))  # Perna direita
        
        return sprite

# Definição dos personagens baseados nas descrições
CHARACTERS = {
    "shadow_hunter": Character(
        name="Caçador das Sombras",
        description="Especialista em emboscadas e movimento furtivo",
        primary_color=(10, 10, 10),      # Preto profundo
        secondary_color=(75, 0, 130),    # Roxo escuro
        accent_color=(47, 79, 79),       # Azul-acinzentado
        stats={
            "speed": 6,        # Mais rápido
            "health": 90,      # Menos vida
            "damage": 30,      # Dano alto
            "stealth": True    # Habilidade especial
        }
    ),
    
    "cyber_engineer": Character(
        name="Engenheira Ciborgue",
        description="Combina tecnologia avançada com combate tático",
        primary_color=(136, 136, 136),   # Cinza metálico
        secondary_color=(57, 255, 20),   # Verde neon
        accent_color=(75, 62, 42),       # Caqui escuro
        stats={
            "speed": 4,        # Velocidade normal
            "health": 110,     # Mais vida (cibernética)
            "damage": 25,      # Dano normal
            "tech": True       # Habilidade especial
        }
    ),
    
    "heavy_titan": Character(
        name="Soldado TITAN",
        description="Tank pesado com armadura reforçada",
        primary_color=(46, 46, 46),      # Grafite metálico
        secondary_color=(85, 107, 47),   # Verde-oliva
        accent_color=(0, 191, 255),      # Azul-reflexivo
        stats={
            "speed": 3,        # Mais lento
            "health": 150,     # Muito mais vida
            "damage": 35,      # Dano muito alto
            "armor": True      # Habilidade especial
        }
    ),
    
    "ghost_sniper": Character(
        name="Francotirador Fantasma",
        description="Especialista em combate à longa distância",
        primary_color=(169, 169, 169),   # Cinza-claro
        secondary_color=(85, 107, 47),   # Verde-musgo
        accent_color=(139, 69, 19),      # Marrom-terra
        stats={
            "speed": 5,        # Velocidade boa
            "health": 85,      # Menos vida
            "damage": 40,      # Dano muito alto
            "precision": True  # Habilidade especial
        }
    ),
    
    "urban_berserker": Character(
        name="Berserker Urbano",
        description="Combatente corpo a corpo com fúria devastadora",
        primary_color=(0, 0, 0),         # Preto
        secondary_color=(139, 0, 0),     # Vermelho-escuro
        accent_color=(51, 51, 51),       # Cinza-chumbo
        stats={
            "speed": 7,        # Muito rápido
            "health": 95,      # Vida moderada
            "damage": 45,      # Dano extremo
            "fury": True       # Habilidade especial
        }
    ),
    
    "rebel_pilot": Character(
        name="Piloto Rebelde",
        description="Ex-aviador adaptado ao combate terrestre",
        primary_color=(139, 69, 19),     # Marrom-couro
        secondary_color=(178, 34, 34),   # Vermelho
        accent_color=(245, 245, 220),    # Bege
        stats={
            "speed": 5,        # Velocidade boa
            "health": 100,     # Vida padrão
            "damage": 28,      # Dano bom
            "mobility": True   # Habilidade especial
        }
    )
}

def get_character_list():
    """Retorna lista de nomes dos personagens disponíveis."""
    return list(CHARACTERS.keys())

def get_character(character_id):
    """Retorna um personagem específico pelo ID."""
    return CHARACTERS.get(character_id, CHARACTERS["shadow_hunter"])

def get_character_names():
    """Retorna lista de nomes dos personagens para exibição."""
    return [char.name for char in CHARACTERS.values()]
