# src/enemy_types.py

import pygame
import random
import math
from modern_graphics import ModernSprite

class EnemyType:
    def __init__(self, name, character_type, primary_color, secondary_color, accent_color, stats, ai_behavior):
        """
        Define um tipo de inimigo moderno.
        """
        self.name = name
        self.character_type = character_type
        self.primary_color = primary_color
        self.secondary_color = secondary_color
        self.accent_color = accent_color
        self.stats = stats
        self.ai_behavior = ai_behavior
        
        # Cria sprite moderno
        self.sprite_generator = ModernSprite(character_type, primary_color, secondary_color, accent_color)
        self.sprite = self.sprite_generator.sprite

# Tipos de inimigos modernos e variados
ENEMY_TYPES = {
    "assault_trooper": EnemyType(
        name="Soldado de Assalto",
        character_type="tactical_operator",
        primary_color=(80, 40, 40),      # Vermelho militar escuro
        secondary_color=(120, 60, 60),   # Vermelho médio
        accent_color=(255, 100, 100),    # Vermelho LED
        stats={
            "health": 80,
            "speed": 4,
            "damage": 20,
            "detection_range": 200,
            "attack_range": 150,
            "accuracy": 0.7
        },
        ai_behavior="aggressive"
    ),
    
    "heavy_gunner": EnemyType(
        name="Artilheiro Pesado",
        character_type="heavy_assault",
        primary_color=(60, 60, 40),      # Verde militar escuro
        secondary_color=(100, 100, 60),  # Verde médio
        accent_color=(150, 255, 150),    # Verde claro
        stats={
            "health": 120,
            "speed": 2,
            "damage": 35,
            "detection_range": 180,
            "attack_range": 200,
            "accuracy": 0.6
        },
        ai_behavior="defensive"
    ),
    
    "stealth_operative": EnemyType(
        name="Operativo Furtivo",
        character_type="stealth_agent",
        primary_color=(40, 40, 60),      # Azul escuro
        secondary_color=(60, 60, 100),   # Azul médio
        accent_color=(100, 150, 255),    # Azul claro
        stats={
            "health": 60,
            "speed": 6,
            "damage": 30,
            "detection_range": 250,
            "attack_range": 120,
            "accuracy": 0.8
        },
        ai_behavior="flanking"
    ),
    
    "cyber_enforcer": EnemyType(
        name="Executor Cibernético",
        character_type="cyber_soldier",
        primary_color=(60, 40, 80),      # Roxo escuro
        secondary_color=(120, 80, 160),  # Roxo médio
        accent_color=(200, 100, 255),    # Roxo neon
        stats={
            "health": 100,
            "speed": 5,
            "damage": 28,
            "detection_range": 220,
            "attack_range": 160,
            "accuracy": 0.75
        },
        ai_behavior="adaptive"
    ),
    
    "support_medic": EnemyType(
        name="Médico de Suporte",
        character_type="combat_medic",
        primary_color=(60, 80, 60),      # Verde médico escuro
        secondary_color=(100, 120, 100), # Verde médico médio
        accent_color=(255, 100, 100),    # Vermelho cruz
        stats={
            "health": 70,
            "speed": 3,
            "damage": 15,
            "detection_range": 160,
            "attack_range": 100,
            "accuracy": 0.5
        },
        ai_behavior="support"
    ),
    
    "demolition_specialist": EnemyType(
        name="Especialista Demolidor",
        character_type="demolition_expert",
        primary_color=(80, 60, 40),      # Marrom escuro
        secondary_color=(120, 100, 60),  # Marrom médio
        accent_color=(255, 200, 100),    # Amarelo explosivo
        stats={
            "health": 90,
            "speed": 3,
            "damage": 40,
            "detection_range": 170,
            "attack_range": 140,
            "accuracy": 0.65
        },
        ai_behavior="explosive"
    )
}

def get_random_enemy_type():
    """Retorna um tipo de inimigo aleatório."""
    return random.choice(list(ENEMY_TYPES.values()))

def get_enemy_type_by_difficulty(difficulty_level):
    """
    Retorna tipos de inimigos baseados no nível de dificuldade.
    difficulty_level: 1 (fácil) a 5 (muito difícil)
    """
    if difficulty_level <= 2:
        # Inimigos mais fracos
        return random.choice([
            ENEMY_TYPES["assault_trooper"],
            ENEMY_TYPES["support_medic"]
        ])
    elif difficulty_level <= 3:
        # Inimigos médios
        return random.choice([
            ENEMY_TYPES["assault_trooper"],
            ENEMY_TYPES["cyber_enforcer"],
            ENEMY_TYPES["support_medic"]
        ])
    elif difficulty_level <= 4:
        # Inimigos difíceis
        return random.choice([
            ENEMY_TYPES["heavy_gunner"],
            ENEMY_TYPES["stealth_operative"],
            ENEMY_TYPES["cyber_enforcer"],
            ENEMY_TYPES["demolition_specialist"]
        ])
    else:
        # Inimigos muito difíceis
        return random.choice([
            ENEMY_TYPES["heavy_gunner"],
            ENEMY_TYPES["stealth_operative"],
            ENEMY_TYPES["demolition_specialist"]
        ])

def create_enemy_squad(num_enemies, difficulty_level=3):
    """
    Cria um esquadrão balanceado de inimigos.
    """
    squad = []
    
    # Garante pelo menos um de cada tipo principal
    if num_enemies >= 3:
        squad.append(ENEMY_TYPES["assault_trooper"])  # DPS
        squad.append(ENEMY_TYPES["heavy_gunner"])     # Tank
        squad.append(ENEMY_TYPES["stealth_operative"]) # Assassino
        
        # Preenche o resto aleatoriamente
        for _ in range(num_enemies - 3):
            squad.append(get_enemy_type_by_difficulty(difficulty_level))
    else:
        # Para poucos inimigos, escolhe aleatoriamente
        for _ in range(num_enemies):
            squad.append(get_enemy_type_by_difficulty(difficulty_level))
    
    return squad

class EnemyVisualEffects:
    """
    Efeitos visuais específicos para inimigos.
    """
    
    @staticmethod
    def draw_health_bar(surface, x, y, current_health, max_health, enemy_type):
        """
        Desenha barra de vida sobre o inimigo.
        """
        bar_width = 30
        bar_height = 4
        
        # Fundo da barra
        bg_rect = pygame.Rect(x - bar_width//2, y - 35, bar_width, bar_height)
        pygame.draw.rect(surface, (40, 40, 40), bg_rect)
        
        # Barra de vida
        health_percent = current_health / max_health
        health_width = int(bar_width * health_percent)
        
        if health_width > 0:
            # Cor baseada no tipo de inimigo
            if enemy_type.ai_behavior == "aggressive":
                color = (255, 100, 100)  # Vermelho
            elif enemy_type.ai_behavior == "defensive":
                color = (100, 255, 100)  # Verde
            elif enemy_type.ai_behavior == "flanking":
                color = (100, 100, 255)  # Azul
            elif enemy_type.ai_behavior == "adaptive":
                color = (200, 100, 255)  # Roxo
            elif enemy_type.ai_behavior == "support":
                color = (255, 255, 100)  # Amarelo
            else:  # explosive
                color = (255, 150, 50)   # Laranja
            
            health_rect = pygame.Rect(x - bar_width//2, y - 35, health_width, bar_height)
            pygame.draw.rect(surface, color, health_rect)
    
    @staticmethod
    def draw_status_indicator(surface, x, y, enemy_type, current_state):
        """
        Desenha indicador de status do inimigo.
        """
        # Ícone baseado no estado
        if current_state == "patrol":
            color = (100, 100, 100)  # Cinza
            size = 2
        elif current_state == "chase":
            color = (255, 255, 100)  # Amarelo
            size = 3
        elif current_state == "attack":
            color = (255, 100, 100)  # Vermelho
            size = 4
        elif current_state == "retreat":
            color = (100, 255, 255)  # Ciano
            size = 2
        else:
            return
        
        # Desenha indicador
        pygame.draw.circle(surface, color, (x + 20, y - 30), size)
    
    @staticmethod
    def draw_detection_range(surface, x, y, detection_range, camera=None):
        """
        Desenha o alcance de detecção (para debug).
        """
        if camera:
            screen_x = x - camera.x
            screen_y = y - camera.y
        else:
            screen_x = x
            screen_y = y
        
        # Círculo semi-transparente
        pygame.draw.circle(surface, (255, 255, 0, 50), (int(screen_x), int(screen_y)), detection_range, 1)

def get_enemy_bullet_type(enemy_type):
    """
    Retorna o tipo de bala baseado no tipo de inimigo.
    """
    if enemy_type.character_type == "cyber_soldier":
        return "energy"
    elif enemy_type.character_type == "demolition_expert":
        return "explosive"
    else:
        return "standard"
