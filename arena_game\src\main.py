# src/main.py

import pygame
import sys
import os
import random
from settings import WIDTH, HEIGHT, FPS, WHITE, WORLD_WIDTH, WORLD_HEIGHT, NUM_BOTS
from entities.player import Player
from entities.bot import Bot
from camera import Camera
from world import World
from utils import draw_text, draw_minimap
from audio import audio_manager

def main():
    pygame.init()
    screen = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("Arena Battle Royale - Mundo Expandido")
    clock = pygame.time.Clock()

    # --- Inicialização dos sistemas ---
    world = World()
    camera = Camera()

    # --- Instanciação do jogador principal ---
    player = Player(
        x=WORLD_WIDTH // 2,
        y=WORLD_HEIGHT // 2,
        sprite_path=os.path.join("assets", "player1.png"),
        controls={
            'up': pygame.K_w,
            'down': pygame.K_s,
            'left': pygame.K_a,
            'right': pygame.K_d,
            'shoot': pygame.K_SPACE
        }
    )

    # --- Criação dos inimigos (bots) com IA avançada ---
    bots = []

    # Primeira passada: cria todos os bots
    for i in range(NUM_BOTS):
        # Posiciona bots aleatoriamente no mundo
        bot_x = random.randint(100, WORLD_WIDTH - 100)
        bot_y = random.randint(100, WORLD_HEIGHT - 100)

        # Evita spawnar muito perto do jogador
        while abs(bot_x - player.rect.x) < 300 and abs(bot_y - player.rect.y) < 300:
            bot_x = random.randint(100, WORLD_WIDTH - 100)
            bot_y = random.randint(100, WORLD_HEIGHT - 100)

        bot = Bot(
            x=bot_x,
            y=bot_y,
            sprite_path=os.path.join("assets", "player2.png"),
            target=player,
            bot_list=bots  # Passa lista para coordenação
        )
        bots.append(bot)

    # Segunda passada: atualiza a lista de bots em cada bot para coordenação
    for bot in bots:
        bot.bot_list = bots

    running = True
    winner = None
    bots_defeated = 0

    while running:
        clock.tick(FPS)
        current_time = pygame.time.get_ticks()

        # --- Eventos de fechamento e controles ---
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_m:  # Tecla M para mutar/desmutar som
                    sound_enabled = audio_manager.toggle_sound()
                    print(f"Som {'ligado' if sound_enabled else 'desligado'}")
            else:
                # Passa eventos para o gerenciador de áudio
                audio_manager.handle_sound_timer_event(event)

        # --- Captura estado atual do teclado ---
        keys = pygame.key.get_pressed()

        # --- Movimentação / Disparo do Jogador ---
        player.handle_movement(keys)
        player.handle_shooting(keys, current_time, audio_manager)

        # --- Atualiza Balas do Jogador ---
        player.update_bullets()

        # --- Atualiza Bots ---
        for bot in bots[:]:
            if bot.life > 0:
                bot.update(current_time)

        # --- Checa Colisões: balas do jogador atingindo bots ---
        for bullet in player.bullets[:]:
            for bot in bots:
                if bot.life > 0 and bullet.rect.colliderect(bot.rect):
                    player.bullets.remove(bullet)
                    bot.take_damage(1)
                    if bot.life <= 0:
                        bots_defeated += 1
                    break

        # --- Checa Colisões: balas dos bots atingindo o jogador ---
        for bot in bots:
            if bot.life > 0:
                for bullet in bot.bullets[:]:
                    if bullet.rect.colliderect(player.rect):
                        bot.bullets.remove(bullet)
                        player.life -= 1

        # --- Verifica condição de vitória/derrota ---
        if player.life <= 0:
            winner = "Você perdeu!"
            running = False
        elif bots_defeated >= NUM_BOTS:
            winner = "Você venceu!"
            running = False

        # --- Atualiza Câmera ---
        camera.update(player)

        # --- Atualiza Sistema de Áudio ---
        audio_manager.update_scheduled_stops()

        # --- Desenho na Tela ---
        # Desenha o mundo
        world.draw(screen, camera)

        # Desenha o jogador
        player.draw(screen, camera)

        # Desenha bots vivos
        for bot in bots:
            if bot.life > 0:
                bot.draw(screen, camera)

        # --- Interface do usuário (UI) ---
        # Desenha informações do jogador
        draw_text(screen, f"Vida: {player.life}", 20, 10, 10)
        draw_text(screen, f"Inimigos: {NUM_BOTS - bots_defeated}/{NUM_BOTS}", 20, 10, 40)

        # Conta bots elite vivos
        elite_bots_alive = sum(1 for bot in bots if bot.life > 0 and bot.is_elite)
        if elite_bots_alive > 0:
            draw_text(screen, f"Elites: {elite_bots_alive}", 18, 10, 70, color=(255, 0, 0))

        # Desenha minimapa
        draw_minimap(screen, camera, player, bots, world)

        # Desenha coordenadas e estado dos bots próximos (debug)
        draw_text(screen, f"Pos: ({int(player.rect.x)}, {int(player.rect.y)})", 16, 10, HEIGHT - 50)

        # Mostra estado dos bots próximos
        nearby_bots = [bot for bot in bots if bot.life > 0 and bot.get_distance_to_target() < 300]
        if nearby_bots:
            states = [bot.state for bot in nearby_bots[:3]]  # Máximo 3
            draw_text(screen, f"Bots próximos: {', '.join(states)}", 14, 10, HEIGHT - 30)

        # Mostra status do som
        sound_status = "🔊" if audio_manager.enabled else "🔇"
        draw_text(screen, f"Som: {sound_status} (M para alternar)", 14, WIDTH - 200, HEIGHT - 30)

        pygame.display.flip()

    # --- Tela de vitória/derrota e espera por reinício ou saída ---
    screen.fill(WHITE)
    if winner:
        draw_text(screen, winner, 40, WIDTH // 2 - len(winner) * 10, HEIGHT // 2 - 20)
    draw_text(screen, f"Inimigos derrotados: {bots_defeated}/{NUM_BOTS}", 24, WIDTH // 2 - 150, HEIGHT // 2 + 20)
    draw_text(screen, "Pressione R para reiniciar ou Q para sair", 24, WIDTH // 2 - 200, HEIGHT // 2 + 60)
    pygame.display.flip()

    waiting = True
    while waiting:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                waiting = False
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r:
                    main()  # reinicia o jogo recursivamente
                    return
                if event.key == pygame.K_q:
                    waiting = False

    # Para todos os sons antes de sair
    audio_manager.stop_all_sounds()
    pygame.quit()
    sys.exit()


if __name__ == "__main__":
    main()
