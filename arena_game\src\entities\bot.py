# src/entities/bot.py

import pygame
from settings import PLAYER_SPEED, MAX_BULLETS
from entities.player import Player
from entities.bullet import Bullet

class Bot(Player):
    def __init__(self, x, y, sprite_path, target):
        """
        x, y: posição inicial do Bot
        sprite_path: caminho do sprite (pode usar o mesmo do player)
        target: instância de Player que o Bot vai perseguir/atacar
        """
        # Chamamos __init__ de Player, mas não definimos controles (não usados)
        super().__init__(x, y, sprite_path, controls={})
        self.target = target

    def ai_move(self):
        """Move o bot em direção (diretamente) ao alvo."""
        if self.rect.x < self.target.rect.x:
            self.rect.x += PLAYER_SPEED
        elif self.rect.x > self.target.rect.x:
            self.rect.x -= PLAYER_SPEED

        if self.rect.y < self.target.rect.y:
            self.rect.y += PLAYER_SPEED
        elif self.rect.y > self.target.rect.y:
            self.rect.y -= PLAYER_SPEED

    def ai_shoot(self, current_time):
        """
        Atira quando estiver aproximadamente alinhado no eixo X
        (você pode incrementar lógica para Y também).
        """
        dx = self.target.rect.centerx - self.rect.centerx
        dy = self.target.rect.centery - self.rect.centery

        # Se estiver alinhado horizontalmente (diferença pequena em y)
        if abs(dy) < 10:
            direction = (1 if dx > 0 else -1, 0)
            if len(self.bullets) < MAX_BULLETS and current_time - self.last_shot > 500:
                bx = self.rect.centerx
                by = self.rect.centery
                bullet = Bullet(bx, by, direction)
                self.bullets.append(bullet)
                self.last_shot = current_time

    def update(self, current_time):
        """
        Chame este método no loop principal, em vez de handle_movement/handle_shooting.
        """
        self.ai_move()
        self.ai_shoot(current_time)
        self.update_bullets()
