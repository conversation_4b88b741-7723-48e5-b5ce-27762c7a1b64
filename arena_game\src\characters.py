# src/characters.py

import pygame
from settings import PLAYER_WIDTH, PLAYER_HEIGHT
from modern_graphics import create_modern_character_sprite

class Character:
    def __init__(self, name, description, character_type, primary_color, secondary_color, accent_color, stats, backstory=""):
        """
        Classe para personagens modernos e realistas.
        """
        self.name = name
        self.description = description
        self.character_type = character_type
        self.primary_color = primary_color
        self.secondary_color = secondary_color
        self.accent_color = accent_color
        self.stats = stats
        self.backstory = backstory

        # Cria sprite moderno e realista
        self.sprite = create_modern_character_sprite(
            character_type, primary_color, secondary_color, accent_color
        )

# Personagens modernos e realistas para Battle Royale
CHARACTERS = {
    "tactical_operator": Character(
        name="Operador Tático",
        description="Soldado de elite com treinamento militar avançado",
        character_type="tactical_operator",
        primary_color=(40, 60, 40),      # Verde militar
        secondary_color=(80, 80, 80),    # Cinza tático
        accent_color=(0, 255, 100),      # Verde LED
        stats={
            "speed": 5,        # Equilibrado
            "health": 100,     # Vida padrão
            "damage": 30,      # Dano bom
            "tactical": True   # Habilidade especial
        },
        backstory="Ex-militar das forças especiais, especialista em operações urbanas e combate tático."
    ),

    "cyber_soldier": Character(
        name="Soldado Cibernético",
        description="Guerreiro do futuro com implantes tecnológicos",
        character_type="cyber_soldier",
        primary_color=(60, 60, 80),      # Azul metálico
        secondary_color=(100, 200, 255), # Azul cibernético
        accent_color=(255, 100, 255),    # Rosa neon
        stats={
            "speed": 4,        # Velocidade moderada
            "health": 120,     # Mais resistente
            "damage": 35,      # Dano alto
            "cyber": True      # Habilidade especial
        },
        backstory="Soldado modificado com implantes cibernéticos que aumentam suas capacidades de combate."
    ),

    "heavy_assault": Character(
        name="Assalto Pesado",
        description="Tank humano com armadura de combate pesada",
        character_type="heavy_assault",
        primary_color=(80, 80, 60),      # Marrom militar
        secondary_color=(120, 120, 100), # Bege armadura
        accent_color=(100, 150, 255),    # Azul visor
        stats={
            "speed": 3,        # Lento
            "health": 160,     # Muito resistente
            "damage": 40,      # Dano muito alto
            "armor": True      # Habilidade especial
        },
        backstory="Especialista em assalto frontal, usa armadura pesada e armas de alto calibre."
    ),

    "stealth_agent": Character(
        name="Agente Furtivo",
        description="Assassino silencioso especializado em infiltração",
        character_type="stealth_agent",
        primary_color=(20, 20, 30),      # Preto furtivo
        secondary_color=(40, 40, 60),    # Azul escuro
        accent_color=(100, 255, 100),    # Verde visão noturna
        stats={
            "speed": 7,        # Muito rápido
            "health": 80,      # Frágil
            "damage": 45,      # Dano letal
            "stealth": True    # Habilidade especial
        },
        backstory="Agente de elite treinado em assassinato e infiltração, mestre da furtividade."
    ),

    "combat_medic": Character(
        name="Médico de Combate",
        description="Suporte médico com treinamento de combate",
        character_type="combat_medic",
        primary_color=(80, 100, 80),     # Verde médico
        secondary_color=(120, 140, 120), # Verde claro
        accent_color=(255, 50, 50),      # Vermelho cruz
        stats={
            "speed": 4,        # Velocidade moderada
            "health": 110,     # Resistente
            "damage": 25,      # Dano moderado
            "healing": True    # Habilidade especial
        },
        backstory="Médico militar que combina habilidades de cura com treinamento de combate."
    ),

    "demolition_expert": Character(
        name="Especialista em Demolição",
        description="Perito em explosivos e destruição tática",
        character_type="demolition_expert",
        primary_color=(100, 60, 40),     # Marrom explosivos
        secondary_color=(140, 100, 60),  # Bege colete
        accent_color=(255, 200, 0),      # Amarelo óculos
        stats={
            "speed": 4,        # Velocidade moderada
            "health": 95,      # Vida moderada
            "damage": 50,      # Dano explosivo
            "explosives": True # Habilidade especial
        },
        backstory="Especialista em explosivos e demolição, capaz de causar destruição massiva."
    )
}

def get_character_list():
    """Retorna lista de nomes dos personagens disponíveis."""
    return list(CHARACTERS.keys())

def get_character(character_id):
    """Retorna um personagem específico pelo ID."""
    return CHARACTERS.get(character_id, CHARACTERS["tactical_operator"])

def get_character_names():
    """Retorna lista de nomes dos personagens para exibição."""
    return [char.name for char in CHARACTERS.values()]
