# src/menu.py

import pygame
import sys
import math
from settings import WIDTH, HEI<PERSON>HT, WHITE, BLACK, UI_BLUE, UI_GREEN, UI_RED, UI_DARK_GRAY
from ui import ui

class GameState:
    MENU = "menu"
    MULTIPLAYER_MENU = "multiplayer_menu"
    CHARACTER_SELECT = "character_select"
    PLAYING = "playing"
    MULTIPLAYER = "multiplayer"
    GAME_OVER = "game_over"
    PAUSED = "paused"

class Menu:
    def __init__(self):
        """
        Inicializa o sistema de menus do jogo.
        """
        self.state = GameState.MENU
        self.player_name = ""
        self.selected_character = None
        self.input_active = False
        self.blink_timer = 0

    def handle_menu_events(self, events):
        """
        Manipula eventos do menu principal.
        """
        for event in events:
            if event.type == pygame.KEYDOWN:
                if self.input_active:
                    if event.key == pygame.K_RETURN:
                        if len(self.player_name.strip()) > 0:
                            return GameState.CHARACTER_SELECT
                    elif event.key == pygame.K_BACKSPACE:
                        self.player_name = self.player_name[:-1]
                    elif event.unicode.isprintable() and len(self.player_name) < 15:
                        self.player_name += event.unicode
                elif event.key == pygame.K_m:
                    if len(self.player_name.strip()) > 0:
                        return GameState.MULTIPLAYER_MENU
                else:
                    if event.key == pygame.K_RETURN:
                        self.input_active = True
                    elif event.key == pygame.K_ESCAPE:
                        return "quit"

            elif event.type == pygame.MOUSEBUTTONDOWN:
                mouse_pos = pygame.mouse.get_pos()

                # Verifica clique na caixa de input
                input_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2, 300, 40)
                if input_rect.collidepoint(mouse_pos):
                    self.input_active = True

                # Verifica cliques nos botões
                if len(self.player_name.strip()) > 0:
                    single_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 + 60, 140, 50)
                    multi_rect = pygame.Rect(WIDTH//2 + 10, HEIGHT//2 + 60, 140, 50)

                    if single_rect.collidepoint(mouse_pos):
                        return GameState.CHARACTER_SELECT
                    elif multi_rect.collidepoint(mouse_pos):
                        return GameState.MULTIPLAYER_MENU

                # Verifica clique no botão sair
                quit_rect = pygame.Rect(WIDTH//2 - 100, HEIGHT//2 + 140, 200, 50)
                if quit_rect.collidepoint(mouse_pos):
                    return "quit"

        return GameState.MENU

    def handle_game_over_events(self, events, stats):
        """
        Manipula eventos da tela de game over.
        """
        for event in events:
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r:
                    return "restart"
                elif event.key == pygame.K_q or event.key == pygame.K_ESCAPE:
                    return "quit"

            elif event.type == pygame.MOUSEBUTTONDOWN:
                mouse_pos = pygame.mouse.get_pos()

                # Botão reiniciar
                restart_rect = pygame.Rect(WIDTH//2 - 120, HEIGHT//2 + 100, 100, 50)
                if restart_rect.collidepoint(mouse_pos):
                    return "restart"

                # Botão sair
                quit_rect = pygame.Rect(WIDTH//2 + 20, HEIGHT//2 + 100, 100, 50)
                if quit_rect.collidepoint(mouse_pos):
                    return "quit"

        return GameState.GAME_OVER

    def draw_menu(self, surface):
        """
        Desenha o menu principal moderno e atraente.
        """
        # Fundo gradiente moderno
        self._draw_modern_background(surface)

        # Logo/Título principal com efeito
        self._draw_main_title(surface)

        # Painel de login moderno
        self._draw_login_panel(surface)

        # Efeitos visuais de fundo
        self._draw_background_effects(surface)

    def _draw_modern_background(self, surface):
        """Desenha fundo moderno com gradiente."""
        # Gradiente de fundo
        for y in range(HEIGHT):
            color_factor = y / HEIGHT
            r = int(20 + color_factor * 15)  # 20 -> 35
            g = int(25 + color_factor * 20)  # 25 -> 45
            b = int(35 + color_factor * 25)  # 35 -> 60
            pygame.draw.line(surface, (r, g, b), (0, y), (WIDTH, y))

        # Padrão de grade sutil
        grid_color = (40, 45, 55)
        for x in range(0, WIDTH, 50):
            pygame.draw.line(surface, grid_color, (x, 0), (x, HEIGHT), 1)
        for y in range(0, HEIGHT, 50):
            pygame.draw.line(surface, grid_color, (0, y), (WIDTH, y), 1)

    def _draw_main_title(self, surface):
        """Desenha título principal com efeitos."""
        # Sombra do título
        shadow_color = (10, 10, 10)
        ui.draw_text(surface, "ARENA BATTLE ROYALE", ui.font_title, shadow_color,
                    WIDTH//2 + 3, 83, center=True)

        # Título principal
        title_color = (255, 255, 255)
        ui.draw_text(surface, "ARENA BATTLE ROYALE", ui.font_title, title_color,
                    WIDTH//2, 80, center=True)

        # Subtítulo com cor accent
        subtitle_color = (100, 200, 255)
        ui.draw_text(surface, "TACTICAL COMBAT EDITION", ui.font_large, subtitle_color,
                    WIDTH//2, 130, center=True)

        # Linha decorativa
        line_color = (100, 200, 255)
        pygame.draw.line(surface, line_color, (WIDTH//2 - 200, 150), (WIDTH//2 + 200, 150), 3)

    def _draw_login_panel(self, surface):
        """Desenha painel de login moderno."""
        # Painel principal
        panel_rect = pygame.Rect(WIDTH//2 - 200, HEIGHT//2 - 80, 400, 200)

        # Fundo do painel com transparência simulada
        panel_color = (40, 50, 65)
        pygame.draw.rect(surface, panel_color, panel_rect)
        pygame.draw.rect(surface, (100, 200, 255), panel_rect, 3)

        # Título do painel
        ui.draw_text(surface, "IDENTIFICAÇÃO DO OPERADOR", ui.font_normal, (200, 200, 200),
                    WIDTH//2, HEIGHT//2 - 50, center=True)

        # Campo de entrada moderno
        input_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 - 10, 300, 50)

        # Fundo do campo
        field_bg_color = (30, 35, 45) if not self.input_active else (35, 45, 60)
        pygame.draw.rect(surface, field_bg_color, input_rect)

        # Borda do campo
        border_color = (100, 200, 255) if self.input_active else (80, 80, 100)
        pygame.draw.rect(surface, border_color, input_rect, 2)

        # Texto do campo
        if self.player_name:
            text_color = (255, 255, 255)
            ui.draw_text(surface, self.player_name, ui.font_normal, text_color,
                        input_rect.centerx, input_rect.centery, center=True)
        else:
            placeholder_color = (120, 120, 140)
            ui.draw_text(surface, "Digite seu callsign...", ui.font_normal, placeholder_color,
                        input_rect.centerx, input_rect.centery, center=True)

        # Cursor piscante moderno
        if self.input_active:
            self.blink_timer += 1
            if self.blink_timer % 60 < 30:
                cursor_x = input_rect.centerx + len(self.player_name) * 7
                cursor_color = (100, 200, 255)
                pygame.draw.line(surface, cursor_color, (cursor_x, input_rect.y + 12),
                               (cursor_x, input_rect.bottom - 12), 3)

        # Botões de ação
        if len(self.player_name.strip()) > 0:
            # Botão Single Player
            single_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 + 60, 140, 50)
            pygame.draw.rect(surface, (50, 150, 50), single_rect)
            pygame.draw.rect(surface, (100, 255, 100), single_rect, 3)
            ui.draw_text(surface, "CAMPANHA", ui.font_normal, (255, 255, 255),
                        single_rect.centerx, single_rect.centery, center=True)

            # Botão Multiplayer
            multi_rect = pygame.Rect(WIDTH//2 + 10, HEIGHT//2 + 60, 140, 50)
            pygame.draw.rect(surface, (50, 100, 150), multi_rect)
            pygame.draw.rect(surface, (100, 150, 255), multi_rect, 3)
            ui.draw_text(surface, "MULTIPLAYER", ui.font_normal, (255, 255, 255),
                        multi_rect.centerx, multi_rect.centery, center=True)

        # Instruções modernas
        instruction_color = (150, 150, 170)
        ui.draw_text(surface, "ENTER para campanha | M para multiplayer | ESC para abortar",
                    ui.font_small, instruction_color, WIDTH//2, HEIGHT//2 + 140, center=True)

    def _draw_background_effects(self, surface):
        """Desenha efeitos visuais de fundo."""
        # Pontos de luz animados
        import time
        current_time = time.time()

        for i in range(8):
            x = 100 + i * 100
            y = 200 + math.sin(current_time + i) * 50
            alpha = int(128 + math.sin(current_time * 2 + i) * 127)

            # Simula transparência com cores mais escuras
            light_color = (
                int(100 * alpha / 255),
                int(200 * alpha / 255),
                int(255 * alpha / 255)
            )
            pygame.draw.circle(surface, light_color, (int(x), int(y)), 3)

        # Linhas de conexão futuristas
        line_color = (50, 100, 150)
        for i in range(4):
            start_x = 50 + i * 200
            end_x = start_x + 150
            y = 400 + math.sin(current_time + i * 0.5) * 20
            pygame.draw.line(surface, line_color, (start_x, int(y)), (end_x, int(y)), 2)

    def draw_game_over(self, surface, stats):
        """
        Desenha a tela de game over moderna e atraente.
        """
        # Fundo gradiente escuro
        self._draw_game_over_background(surface)

        # Painel principal moderno
        self._draw_game_over_panel(surface, stats)

        # Efeitos visuais baseados no resultado
        self._draw_game_over_effects(surface, stats['victory'])

    def _draw_game_over_background(self, surface):
        """Desenha fundo da tela de game over."""
        # Gradiente escuro
        for y in range(HEIGHT):
            color_factor = y / HEIGHT
            r = int(10 + color_factor * 20)  # 10 -> 30
            g = int(10 + color_factor * 15)  # 10 -> 25
            b = int(15 + color_factor * 25)  # 15 -> 40
            pygame.draw.line(surface, (r, g, b), (0, y), (WIDTH, y))

        # Overlay semi-transparente
        overlay = pygame.Surface((WIDTH, HEIGHT))
        overlay.set_alpha(150)
        overlay.fill((0, 0, 0))
        surface.blit(overlay, (0, 0))

    def _draw_game_over_panel(self, surface, stats):
        """Desenha painel principal de game over."""
        # Painel central maior
        panel_rect = pygame.Rect(WIDTH//2 - 250, HEIGHT//2 - 200, 500, 400)

        # Fundo do painel
        panel_color = (30, 40, 55)
        pygame.draw.rect(surface, panel_color, panel_rect)

        # Borda com cor baseada no resultado
        border_color = (100, 255, 100) if stats['victory'] else (255, 100, 100)
        pygame.draw.rect(surface, border_color, panel_rect, 4)

        # Título principal
        title = "MISSÃO CUMPRIDA" if stats['victory'] else "OPERAÇÃO FALHOU"
        title_color = (100, 255, 100) if stats['victory'] else (255, 100, 100)

        # Sombra do título
        ui.draw_text(surface, title, ui.font_title, (10, 10, 10),
                    WIDTH//2 + 2, HEIGHT//2 - 162, center=True)
        ui.draw_text(surface, title, ui.font_title, title_color,
                    WIDTH//2, HEIGHT//2 - 160, center=True)

        # Subtítulo
        subtitle = "VITÓRIA TÁTICA" if stats['victory'] else "KIA - KILLED IN ACTION"
        subtitle_color = (200, 200, 200)
        ui.draw_text(surface, subtitle, ui.font_large, subtitle_color,
                    WIDTH//2, HEIGHT//2 - 120, center=True)

        # Nome do operador
        ui.draw_text(surface, f"OPERADOR: {self.player_name.upper()}", ui.font_normal, (255, 255, 255),
                    WIDTH//2, HEIGHT//2 - 80, center=True)

        # Linha separadora
        line_color = border_color
        pygame.draw.line(surface, line_color, (WIDTH//2 - 200, HEIGHT//2 - 60),
                        (WIDTH//2 + 200, HEIGHT//2 - 60), 2)

        # Estatísticas em formato militar
        self._draw_mission_stats(surface, stats, WIDTH//2, HEIGHT//2 - 30)

        # Botões modernos
        self._draw_game_over_buttons(surface, stats['victory'])

    def _draw_mission_stats(self, surface, stats, center_x, start_y):
        """Desenha estatísticas da missão."""
        # Título das estatísticas
        ui.draw_text(surface, "RELATÓRIO DA MISSÃO", ui.font_normal, (200, 200, 200),
                    center_x, start_y, center=True)

        # Estatísticas detalhadas
        stats_data = [
            ("HOSTIS ELIMINADOS", f"{stats['bots_killed']}/{stats['total_bots']}"),
            ("TEMPO EM CAMPO", stats['time_played']),
            ("PRECISÃO DE TIRO", f"{stats['accuracy']}%"),
            ("INTEGRIDADE FÍSICA", f"{stats['health_remaining']}%")
        ]

        for i, (label, value) in enumerate(stats_data):
            y_pos = start_y + 40 + i * 30

            # Label
            ui.draw_text(surface, label, ui.font_small, (150, 150, 150),
                        center_x - 80, y_pos, center=False)

            # Valor com cor baseada na performance
            if "ELIMINADOS" in label:
                value_color = (100, 255, 100) if stats['bots_killed'] >= stats['total_bots'] else (255, 200, 100)
            elif "PRECISÃO" in label:
                accuracy = stats['accuracy']
                if accuracy >= 70:
                    value_color = (100, 255, 100)
                elif accuracy >= 40:
                    value_color = (255, 255, 100)
                else:
                    value_color = (255, 150, 100)
            elif "INTEGRIDADE" in label:
                health = stats['health_remaining']
                if health >= 70:
                    value_color = (100, 255, 100)
                elif health >= 30:
                    value_color = (255, 255, 100)
                else:
                    value_color = (255, 100, 100)
            else:
                value_color = (255, 255, 255)

            ui.draw_text(surface, value, ui.font_small, value_color,
                        center_x + 80, y_pos, center=False)

    def _draw_game_over_buttons(self, surface, victory):
        """Desenha botões da tela de game over."""
        # Botão reiniciar
        restart_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 + 120, 120, 50)
        restart_color = (50, 150, 50)
        pygame.draw.rect(surface, restart_color, restart_rect)
        pygame.draw.rect(surface, (100, 255, 100), restart_rect, 3)
        ui.draw_text(surface, "NOVA MISSÃO", ui.font_normal, (255, 255, 255),
                    restart_rect.centerx, restart_rect.centery, center=True)
        ui.draw_text(surface, "(R)", ui.font_small, (200, 200, 200),
                    restart_rect.centerx, restart_rect.centery + 20, center=True)

        # Botão sair
        quit_rect = pygame.Rect(WIDTH//2 + 30, HEIGHT//2 + 120, 120, 50)
        quit_color = (150, 50, 50)
        pygame.draw.rect(surface, quit_color, quit_rect)
        pygame.draw.rect(surface, (255, 100, 100), quit_rect, 3)
        ui.draw_text(surface, "ABORTAR", ui.font_normal, (255, 255, 255),
                    quit_rect.centerx, quit_rect.centery, center=True)
        ui.draw_text(surface, "(Q)", ui.font_small, (200, 200, 200),
                    quit_rect.centerx, quit_rect.centery + 20, center=True)

        # Instruções
        instruction_color = (150, 150, 170)
        ui.draw_text(surface, "R para nova missão | Q para retornar à base",
                    ui.font_small, instruction_color, WIDTH//2, HEIGHT//2 + 190, center=True)

    def _draw_game_over_effects(self, surface, victory):
        """Desenha efeitos visuais baseados no resultado."""
        import time
        current_time = time.time()

        if victory:
            # Efeitos de vitória (partículas douradas)
            for i in range(12):
                x = WIDTH//2 + math.sin(current_time + i * 0.5) * 200
                y = 100 + i * 40 + math.cos(current_time * 2 + i) * 20
                size = 2 + math.sin(current_time * 3 + i) * 2

                # Partículas douradas
                gold_color = (255, 215, 0)
                pygame.draw.circle(surface, gold_color, (int(x), int(y)), int(size))
        else:
            # Efeitos de derrota (partículas vermelhas)
            for i in range(8):
                x = WIDTH//2 + math.sin(current_time + i * 0.8) * 150
                y = 150 + i * 50 + math.cos(current_time * 1.5 + i) * 30
                size = 1 + math.sin(current_time * 4 + i) * 1.5

                # Partículas vermelhas
                red_color = (255, 100, 100)
                pygame.draw.circle(surface, red_color, (int(x), int(y)), int(size))

    def draw_pause_menu(self, surface):
        """
        Desenha o menu de pausa.
        """
        # Fundo semi-transparente
        overlay = pygame.Surface((WIDTH, HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        surface.blit(overlay, (0, 0))

        # Painel central
        panel_rect = pygame.Rect(WIDTH//2 - 150, HEIGHT//2 - 100, 300, 200)
        pygame.draw.rect(surface, UI_DARK_GRAY, panel_rect)
        pygame.draw.rect(surface, WHITE, panel_rect, 3)

        # Título
        ui.draw_text(surface, "JOGO PAUSADO", ui.font_large, WHITE,
                    WIDTH//2, HEIGHT//2 - 60, center=True)

        # Instruções
        instructions = [
            "ESC - Continuar",
            "R - Reiniciar",
            "Q - Sair para menu"
        ]

        for i, instruction in enumerate(instructions):
            ui.draw_text(surface, instruction, ui.font_normal, UI_BLUE,
                        WIDTH//2, HEIGHT//2 - 20 + i * 30, center=True)

# Instância global do menu
menu = Menu()
