# src/network_client.py

import socket
import json
import threading
import time

class NetworkClient:
    def __init__(self):
        """
        Cliente de rede para conectar ao servidor multiplayer.
        """
        self.socket = None
        self.connected = False
        self.player_id = None
        self.server_host = None
        self.server_port = None
        
        # Estado do jogo recebido do servidor
        self.game_state = {}
        self.last_update = 0
        
        # Thread para receber dados
        self.receive_thread = None
        self.running = False
        
    def connect(self, host, port):
        """Conecta ao servidor."""
        try:
            self.server_host = host
            self.server_port = port
            
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5.0)  # Timeout de 5 segundos
            
            print(f"Conectando ao servidor {host}:{port}...")
            self.socket.connect((host, port))
            
            self.connected = True
            self.running = True
            
            # Inicia thread para receber dados
            self.receive_thread = threading.Thread(target=self.receive_loop)
            self.receive_thread.daemon = True
            self.receive_thread.start()
            
            print("Conectado ao servidor!")
            return True
            
        except Exception as e:
            print(f"Erro ao conectar: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """Desconecta do servidor."""
        self.running = False
        self.connected = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        print("Desconectado do servidor")
    
    def send_message(self, message):
        """Envia mensagem para o servidor."""
        if not self.connected or not self.socket:
            return None
        
        try:
            data = json.dumps(message).encode('utf-8')
            self.socket.send(data)
            return True
        except Exception as e:
            print(f"Erro ao enviar mensagem: {e}")
            self.connected = False
            return False
    
    def receive_loop(self):
        """Loop para receber dados do servidor."""
        while self.running and self.connected:
            try:
                data = self.socket.recv(4096).decode('utf-8')
                if not data:
                    break
                
                try:
                    response = json.loads(data)
                    self.process_server_response(response)
                except json.JSONDecodeError:
                    print(f"Resposta inválida do servidor: {data}")
                    
            except socket.timeout:
                continue
            except Exception as e:
                print(f"Erro ao receber dados: {e}")
                break
        
        self.connected = False
    
    def process_server_response(self, response):
        """Processa resposta do servidor."""
        if 'game_state' in response:
            self.game_state = response
            self.last_update = time.time()
    
    def join_game(self, player_name, character_type):
        """Entra no jogo."""
        message = {
            'type': 'join',
            'name': player_name,
            'character': character_type
        }
        
        if self.send_message(message):
            # Aguarda resposta
            start_time = time.time()
            while time.time() - start_time < 5.0:  # Timeout de 5 segundos
                if self.game_state and 'player_id' in self.game_state:
                    self.player_id = self.game_state['player_id']
                    return True
                time.sleep(0.1)
        
        return False
    
    def update_player(self, x, y, health, ammo):
        """Atualiza posição e estado do jogador."""
        if not self.player_id:
            return False
        
        message = {
            'type': 'update_player',
            'player_id': self.player_id,
            'x': x,
            'y': y,
            'health': health,
            'ammo': ammo
        }
        
        return self.send_message(message)
    
    def shoot(self, x, y, dx, dy, bullet_type='standard'):
        """Envia comando de tiro."""
        if not self.player_id:
            return False
        
        message = {
            'type': 'shoot',
            'player_id': self.player_id,
            'x': x,
            'y': y,
            'dx': dx,
            'dy': dy,
            'bullet_type': bullet_type
        }
        
        return self.send_message(message)
    
    def request_game_state(self):
        """Solicita estado atual do jogo."""
        message = {
            'type': 'get_game_state'
        }
        
        return self.send_message(message)
    
    def get_players(self):
        """Retorna lista de jogadores."""
        if 'players' in self.game_state:
            return self.game_state['players']
        return {}
    
    def get_bots(self):
        """Retorna lista de bots."""
        if 'bots' in self.game_state:
            return self.game_state['bots']
        return {}
    
    def get_bullets(self):
        """Retorna lista de balas."""
        if 'bullets' in self.game_state:
            return self.game_state['bullets']
        return {}
    
    def get_my_player(self):
        """Retorna dados do próprio jogador."""
        players = self.get_players()
        if self.player_id and self.player_id in players:
            return players[self.player_id]
        return None
    
    def get_other_players(self):
        """Retorna outros jogadores (exceto o próprio)."""
        players = self.get_players()
        other_players = {}
        
        for player_id, player_data in players.items():
            if player_id != self.player_id:
                other_players[player_id] = player_data
        
        return other_players
    
    def is_game_active(self):
        """Verifica se o jogo está ativo."""
        return self.game_state.get('game_state') == 'playing'
    
    def is_game_over(self):
        """Verifica se o jogo terminou."""
        return self.game_state.get('game_state') == 'game_over'
    
    def get_connection_status(self):
        """Retorna status da conexão."""
        return {
            'connected': self.connected,
            'player_id': self.player_id,
            'server': f"{self.server_host}:{self.server_port}" if self.server_host else None,
            'last_update': self.last_update
        }

class ServerDiscovery:
    """Classe para descobrir servidores na rede local."""
    
    @staticmethod
    def find_servers(timeout=3):
        """
        Procura servidores na rede local.
        Retorna lista de (ip, porta) dos servidores encontrados.
        """
        servers = []
        
        # Tenta IPs comuns da rede local
        import subprocess
        import platform
        
        try:
            # Obtém IP local
            if platform.system() == "Windows":
                result = subprocess.run(['ipconfig'], capture_output=True, text=True)
                output = result.stdout
            else:
                result = subprocess.run(['ifconfig'], capture_output=True, text=True)
                output = result.stdout
            
            # Extrai base do IP (ex: 192.168.1.x)
            import re
            ip_pattern = r'192\.168\.\d+\.\d+'
            matches = re.findall(ip_pattern, output)
            
            if matches:
                base_ip = '.'.join(matches[0].split('.')[:-1])
                
                # Testa IPs na faixa
                for i in range(1, 255):
                    ip = f"{base_ip}.{i}"
                    if ServerDiscovery.test_server(ip, 12345, timeout=0.1):
                        servers.append((ip, 12345))
                        
        except Exception as e:
            print(f"Erro na descoberta de servidores: {e}")
        
        return servers
    
    @staticmethod
    def test_server(host, port, timeout=1):
        """Testa se há um servidor no endereço."""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except:
            return False
    
    @staticmethod
    def get_local_ip():
        """Obtém IP local da máquina."""
        try:
            # Conecta a um endereço externo para descobrir IP local
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.connect(("*******", 80))
            local_ip = sock.getsockname()[0]
            sock.close()
            return local_ip
        except:
            return "127.0.0.1"

# Instância global do cliente
network_client = NetworkClient()
