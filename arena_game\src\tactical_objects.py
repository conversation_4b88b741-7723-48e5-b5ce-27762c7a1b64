# src/tactical_objects.py

import pygame
import math
import random
from settings import WORLD_WIDTH, WORLD_HEIGHT

class TacticalObject:
    def __init__(self, x, y, width, height, obj_type, color_scheme):
        """
        Objeto tático 3D com colisões.
        """
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.obj_type = obj_type
        self.color_scheme = color_scheme
        
        # Retângulo de colisão
        self.collision_rect = pygame.Rect(x, y, width, height)
        
        # Propriedades 3D
        self.depth = self.calculate_depth()
        self.shadow_offset = 8
        
    def calculate_depth(self):
        """Calcula profundidade baseada no tipo."""
        depth_map = {
            "concrete_barrier": 25,
            "metal_container": 40,
            "building_corner": 60,
            "vehicle_wreck": 30,
            "concrete_pillar": 50,
            "sandbags": 20,
            "metal_crate": 35,
            "wall_section": 45
        }
        return depth_map.get(self.obj_type, 30)
    
    def draw_3d(self, surface, camera=None):
        """Desenha objeto com efeito 3D."""
        if camera:
            screen_x = self.x - camera.x
            screen_y = self.y - camera.y
        else:
            screen_x = self.x
            screen_y = self.y
        
        # Só desenha se estiver visível
        if -100 <= screen_x <= 900 and -100 <= screen_y <= 700:
            if self.obj_type == "concrete_barrier":
                self._draw_concrete_barrier(surface, screen_x, screen_y)
            elif self.obj_type == "metal_container":
                self._draw_metal_container(surface, screen_x, screen_y)
            elif self.obj_type == "building_corner":
                self._draw_building_corner(surface, screen_x, screen_y)
            elif self.obj_type == "vehicle_wreck":
                self._draw_vehicle_wreck(surface, screen_x, screen_y)
            elif self.obj_type == "concrete_pillar":
                self._draw_concrete_pillar(surface, screen_x, screen_y)
            elif self.obj_type == "sandbags":
                self._draw_sandbags(surface, screen_x, screen_y)
            elif self.obj_type == "metal_crate":
                self._draw_metal_crate(surface, screen_x, screen_y)
            elif self.obj_type == "wall_section":
                self._draw_wall_section(surface, screen_x, screen_y)
    
    def _draw_concrete_barrier(self, surface, x, y):
        """Desenha barreira de concreto 3D."""
        # Sombra
        shadow_rect = pygame.Rect(x + self.shadow_offset, y + self.shadow_offset, self.width, self.height)
        pygame.draw.rect(surface, (20, 20, 20), shadow_rect)
        
        # Base
        base_color = self.color_scheme["primary"]
        pygame.draw.rect(surface, base_color, (x, y, self.width, self.height))
        
        # Efeito 3D - topo
        top_color = tuple(min(255, c + 40) for c in base_color)
        pygame.draw.polygon(surface, top_color, [
            (x, y), (x + self.width, y), 
            (x + self.width - 8, y - 8), (x - 8, y - 8)
        ])
        
        # Efeito 3D - lateral direita
        side_color = tuple(max(0, c - 30) for c in base_color)
        pygame.draw.polygon(surface, side_color, [
            (x + self.width, y), (x + self.width, y + self.height),
            (x + self.width - 8, y + self.height - 8), (x + self.width - 8, y - 8)
        ])
        
        # Detalhes (rachaduras)
        detail_color = tuple(max(0, c - 50) for c in base_color)
        pygame.draw.line(surface, detail_color, (x + 10, y + 5), (x + 30, y + 15), 2)
        pygame.draw.line(surface, detail_color, (x + 20, y + 20), (x + 40, y + 35), 2)
    
    def _draw_metal_container(self, surface, x, y):
        """Desenha container metálico 3D."""
        # Sombra
        shadow_rect = pygame.Rect(x + self.shadow_offset, y + self.shadow_offset, self.width, self.height)
        pygame.draw.rect(surface, (15, 15, 15), shadow_rect)
        
        # Base
        base_color = self.color_scheme["primary"]
        pygame.draw.rect(surface, base_color, (x, y, self.width, self.height))
        
        # Efeito 3D - topo
        top_color = tuple(min(255, c + 50) for c in base_color)
        pygame.draw.polygon(surface, top_color, [
            (x, y), (x + self.width, y), 
            (x + self.width - 12, y - 12), (x - 12, y - 12)
        ])
        
        # Efeito 3D - lateral direita
        side_color = tuple(max(0, c - 40) for c in base_color)
        pygame.draw.polygon(surface, side_color, [
            (x + self.width, y), (x + self.width, y + self.height),
            (x + self.width - 12, y + self.height - 12), (x + self.width - 12, y - 12)
        ])
        
        # Detalhes metálicos
        detail_color = self.color_scheme["accent"]
        # Linhas horizontais
        for i in range(3):
            line_y = y + 15 + i * 20
            pygame.draw.line(surface, detail_color, (x + 5, line_y), (x + self.width - 5, line_y), 2)
        
        # Fechadura
        pygame.draw.rect(surface, (100, 100, 100), (x + self.width - 15, y + self.height//2 - 5, 8, 10))
    
    def _draw_building_corner(self, surface, x, y):
        """Desenha canto de prédio 3D."""
        # Sombra
        shadow_rect = pygame.Rect(x + self.shadow_offset, y + self.shadow_offset, self.width, self.height)
        pygame.draw.rect(surface, (10, 10, 10), shadow_rect)
        
        # Base
        base_color = self.color_scheme["primary"]
        pygame.draw.rect(surface, base_color, (x, y, self.width, self.height))
        
        # Efeito 3D - topo
        top_color = tuple(min(255, c + 30) for c in base_color)
        pygame.draw.polygon(surface, top_color, [
            (x, y), (x + self.width, y), 
            (x + self.width - 15, y - 15), (x - 15, y - 15)
        ])
        
        # Efeito 3D - lateral direita
        side_color = tuple(max(0, c - 35) for c in base_color)
        pygame.draw.polygon(surface, side_color, [
            (x + self.width, y), (x + self.width, y + self.height),
            (x + self.width - 15, y + self.height - 15), (x + self.width - 15, y - 15)
        ])
        
        # Janelas
        window_color = self.color_scheme["accent"]
        for row in range(2):
            for col in range(3):
                win_x = x + 15 + col * 25
                win_y = y + 20 + row * 30
                pygame.draw.rect(surface, window_color, (win_x, win_y, 12, 16))
                pygame.draw.rect(surface, (50, 50, 50), (win_x, win_y, 12, 16), 1)
    
    def _draw_vehicle_wreck(self, surface, x, y):
        """Desenha destroço de veículo 3D."""
        # Sombra
        shadow_rect = pygame.Rect(x + self.shadow_offset, y + self.shadow_offset, self.width, self.height)
        pygame.draw.ellipse(surface, (20, 20, 20), shadow_rect)
        
        # Corpo do veículo
        base_color = self.color_scheme["primary"]
        pygame.draw.ellipse(surface, base_color, (x, y, self.width, self.height))
        
        # Efeito 3D - topo
        top_color = tuple(min(255, c + 40) for c in base_color)
        top_rect = pygame.Rect(x + 5, y - 8, self.width - 10, self.height - 5)
        pygame.draw.ellipse(surface, top_color, top_rect)
        
        # Janelas quebradas
        pygame.draw.rect(surface, (30, 30, 50), (x + 10, y + 8, 20, 12))
        pygame.draw.rect(surface, (30, 30, 50), (x + self.width - 30, y + 8, 20, 12))
        
        # Danos e ferrugem
        damage_color = tuple(max(0, c - 60) for c in base_color)
        pygame.draw.circle(surface, damage_color, (x + 15, y + 20), 8)
        pygame.draw.circle(surface, damage_color, (x + self.width - 20, y + 15), 6)
        
        # Rodas
        pygame.draw.circle(surface, (40, 40, 40), (x + 15, y + self.height - 5), 8)
        pygame.draw.circle(surface, (40, 40, 40), (x + self.width - 15, y + self.height - 5), 8)
    
    def _draw_concrete_pillar(self, surface, x, y):
        """Desenha pilar de concreto 3D."""
        # Sombra
        shadow_rect = pygame.Rect(x + self.shadow_offset, y + self.shadow_offset, self.width, self.height)
        pygame.draw.rect(surface, (15, 15, 15), shadow_rect)
        
        # Base
        base_color = self.color_scheme["primary"]
        pygame.draw.rect(surface, base_color, (x, y, self.width, self.height))
        
        # Efeito 3D - topo
        top_color = tuple(min(255, c + 45) for c in base_color)
        pygame.draw.polygon(surface, top_color, [
            (x, y), (x + self.width, y), 
            (x + self.width - 10, y - 10), (x - 10, y - 10)
        ])
        
        # Efeito 3D - lateral direita
        side_color = tuple(max(0, c - 35) for c in base_color)
        pygame.draw.polygon(surface, side_color, [
            (x + self.width, y), (x + self.width, y + self.height),
            (x + self.width - 10, y + self.height - 10), (x + self.width - 10, y - 10)
        ])
        
        # Detalhes estruturais
        detail_color = tuple(max(0, c - 40) for c in base_color)
        # Linhas verticais
        for i in range(2):
            line_x = x + 8 + i * (self.width - 16)
            pygame.draw.line(surface, detail_color, (line_x, y + 5), (line_x, y + self.height - 5), 2)
    
    def _draw_sandbags(self, surface, x, y):
        """Desenha sacos de areia 3D."""
        # Sombra
        shadow_rect = pygame.Rect(x + self.shadow_offset, y + self.shadow_offset, self.width, self.height)
        pygame.draw.rect(surface, (25, 20, 15), shadow_rect)
        
        # Sacos individuais
        base_color = self.color_scheme["primary"]
        bag_width = self.width // 3
        bag_height = self.height // 2
        
        # Primeira fileira
        for i in range(3):
            bag_x = x + i * bag_width
            bag_y = y + bag_height
            pygame.draw.ellipse(surface, base_color, (bag_x, bag_y, bag_width, bag_height))
            
            # Efeito 3D
            top_color = tuple(min(255, c + 30) for c in base_color)
            pygame.draw.ellipse(surface, top_color, (bag_x + 2, bag_y - 4, bag_width - 4, bag_height - 2))
        
        # Segunda fileira (offset)
        for i in range(2):
            bag_x = x + bag_width//2 + i * bag_width
            bag_y = y
            pygame.draw.ellipse(surface, base_color, (bag_x, bag_y, bag_width, bag_height))
            
            # Efeito 3D
            top_color = tuple(min(255, c + 30) for c in base_color)
            pygame.draw.ellipse(surface, top_color, (bag_x + 2, bag_y - 4, bag_width - 4, bag_height - 2))
    
    def _draw_metal_crate(self, surface, x, y):
        """Desenha caixa metálica 3D."""
        # Sombra
        shadow_rect = pygame.Rect(x + self.shadow_offset, y + self.shadow_offset, self.width, self.height)
        pygame.draw.rect(surface, (20, 20, 20), shadow_rect)
        
        # Base
        base_color = self.color_scheme["primary"]
        pygame.draw.rect(surface, base_color, (x, y, self.width, self.height))
        
        # Efeito 3D - topo
        top_color = tuple(min(255, c + 50) for c in base_color)
        pygame.draw.polygon(surface, top_color, [
            (x, y), (x + self.width, y), 
            (x + self.width - 8, y - 8), (x - 8, y - 8)
        ])
        
        # Efeito 3D - lateral direita
        side_color = tuple(max(0, c - 40) for c in base_color)
        pygame.draw.polygon(surface, side_color, [
            (x + self.width, y), (x + self.width, y + self.height),
            (x + self.width - 8, y + self.height - 8), (x + self.width - 8, y - 8)
        ])
        
        # Detalhes metálicos
        detail_color = self.color_scheme["accent"]
        # Bordas reforçadas
        pygame.draw.rect(surface, detail_color, (x + 3, y + 3, self.width - 6, 3))
        pygame.draw.rect(surface, detail_color, (x + 3, y + self.height - 6, self.width - 6, 3))
        pygame.draw.rect(surface, detail_color, (x + 3, y + 3, 3, self.height - 6))
        pygame.draw.rect(surface, detail_color, (x + self.width - 6, y + 3, 3, self.height - 6))
    
    def _draw_wall_section(self, surface, x, y):
        """Desenha seção de parede 3D."""
        # Sombra
        shadow_rect = pygame.Rect(x + self.shadow_offset, y + self.shadow_offset, self.width, self.height)
        pygame.draw.rect(surface, (15, 15, 15), shadow_rect)
        
        # Base
        base_color = self.color_scheme["primary"]
        pygame.draw.rect(surface, base_color, (x, y, self.width, self.height))
        
        # Efeito 3D - topo
        top_color = tuple(min(255, c + 35) for c in base_color)
        pygame.draw.polygon(surface, top_color, [
            (x, y), (x + self.width, y), 
            (x + self.width - 12, y - 12), (x - 12, y - 12)
        ])
        
        # Efeito 3D - lateral direita
        side_color = tuple(max(0, c - 30) for c in base_color)
        pygame.draw.polygon(surface, side_color, [
            (x + self.width, y), (x + self.width, y + self.height),
            (x + self.width - 12, y + self.height - 12), (x + self.width - 12, y - 12)
        ])
        
        # Textura de tijolos
        brick_color = tuple(max(0, c - 20) for c in base_color)
        for row in range(self.height // 15):
            for col in range(self.width // 25):
                brick_x = x + col * 25 + (10 if row % 2 else 0)
                brick_y = y + row * 15
                if brick_x < x + self.width - 20:
                    pygame.draw.rect(surface, brick_color, (brick_x, brick_y, 20, 12), 1)

def create_tactical_map():
    """Cria mapa tático com objetos 3D estratégicos."""
    objects = []
    
    # Esquemas de cores para diferentes materiais
    concrete_scheme = {"primary": (120, 120, 130), "accent": (80, 80, 90)}
    metal_scheme = {"primary": (100, 110, 120), "accent": (150, 160, 170)}
    building_scheme = {"primary": (80, 90, 100), "accent": (200, 220, 255)}
    vehicle_scheme = {"primary": (80, 60, 40), "accent": (120, 100, 80)}
    sand_scheme = {"primary": (160, 140, 100), "accent": (180, 160, 120)}
    
    # Posições estratégicas para objetos táticos
    tactical_positions = [
        # Centro do mapa - área de controle
        (WORLD_WIDTH//2 - 100, WORLD_HEIGHT//2 - 50, 80, 40, "metal_container", metal_scheme),
        (WORLD_WIDTH//2 + 50, WORLD_HEIGHT//2 - 30, 60, 60, "concrete_pillar", concrete_scheme),
        
        # Cantos estratégicos
        (200, 200, 120, 80, "building_corner", building_scheme),
        (WORLD_WIDTH - 300, 200, 100, 60, "wall_section", concrete_scheme),
        (200, WORLD_HEIGHT - 300, 90, 50, "vehicle_wreck", vehicle_scheme),
        (WORLD_WIDTH - 250, WORLD_HEIGHT - 280, 80, 40, "metal_crate", metal_scheme),
        
        # Pontos de cobertura intermediários
        (500, 400, 100, 30, "sandbags", sand_scheme),
        (800, 600, 70, 70, "concrete_barrier", concrete_scheme),
        (1200, 300, 60, 80, "concrete_pillar", concrete_scheme),
        (1500, 800, 110, 50, "metal_container", metal_scheme),
        
        # Corredores e chokepoints
        (700, 200, 40, 100, "wall_section", concrete_scheme),
        (1000, 500, 80, 40, "concrete_barrier", concrete_scheme),
        (400, 700, 60, 60, "metal_crate", metal_scheme),
        (1300, 600, 90, 35, "sandbags", sand_scheme),
        
        # Áreas de flanqueamento
        (300, 500, 50, 80, "concrete_pillar", concrete_scheme),
        (1600, 400, 70, 50, "vehicle_wreck", vehicle_scheme),
        (600, 800, 80, 40, "metal_container", metal_scheme),
        (1100, 150, 60, 60, "concrete_barrier", concrete_scheme),
    ]
    
    for x, y, w, h, obj_type, color_scheme in tactical_positions:
        obj = TacticalObject(x, y, w, h, obj_type, color_scheme)
        objects.append(obj)
    
    return objects
