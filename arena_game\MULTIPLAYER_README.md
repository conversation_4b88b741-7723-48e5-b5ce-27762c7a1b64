# 🎮 Arena Battle Royale - Multiplayer Local

## 🌐 Como Jogar Multiplayer

### 📋 Requisitos
- Todos os computadores devem estar na **mesma rede local** (WiFi ou cabo)
- **Python 3.7+** instalado em todas as máquinas
- **Pygame** instalado (`pip install pygame`)
- **Porta 12345** liberada no firewall

### 🖥️ Configurando o Servidor (Host)

#### Opção 1: Script Automático
```bash
# Na pasta do jogo, execute:
python start_server.py
```

#### Opção 2: Manual
```bash
# Na pasta src/, execute:
python server.py
```

### 📱 Conectando Clientes

1. **Inicie o jogo** em cada computador cliente
2. **Digite seu nickname** no menu principal
3. **Clique em "MULTIPLAYER"** ou pressione **M**
4. **Escolha uma opção**:
   - **Busca Automática**: O jogo procura servidores na rede
   - **Conexão Manual**: Digite o IP do servidor manualmente

### 🔧 Configuração de Rede

#### 🔍 Descobrindo o IP do Servidor
O script `start_server.py` mostra automaticamente o IP local:
```
IP local da máquina: *************
Outros jogadores devem conectar em: *************:12345
```

#### 🔥 Configuração do Firewall (Windows)
1. Abra **Windows Defender Firewall**
2. Clique em **"Permitir um aplicativo..."**
3. Adicione **Python** à lista de exceções
4. Ou execute como administrador:
```cmd
netsh advfirewall firewall add rule name="Arena Battle Royale" dir=in action=allow protocol=TCP localport=12345
```

#### 🐧 Configuração do Firewall (Linux)
```bash
# Ubuntu/Debian
sudo ufw allow 12345

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=12345/tcp
sudo firewall-cmd --reload
```

### 🎮 Gameplay Multiplayer

#### 👥 Jogadores
- **Máximo**: 4 jogadores simultâneos
- **Mínimo**: 1 jogador (inicia automaticamente)
- **Respawn**: Não há (Battle Royale)

#### 🤖 Inimigos
- **8 bots IA** compartilhados entre todos os jogadores
- **Tipos variados**: Soldados, Snipers, Tanques, etc.
- **IA coordenada**: Atacam o jogador mais próximo

#### 🏆 Vitória
- **Cooperativa**: Todos os jogadores vs bots
- **Vitória**: Eliminar todos os 8 bots
- **Derrota**: Todos os jogadores morrem

### 🔧 Solução de Problemas

#### ❌ "Não consegue conectar"
1. **Verifique o IP**: Use `ipconfig` (Windows) ou `ifconfig` (Linux)
2. **Teste a conexão**: `ping [IP_DO_SERVIDOR]`
3. **Firewall**: Libere a porta 12345
4. **Rede**: Certifique-se que estão na mesma rede

#### ❌ "Servidor não encontrado"
1. **Busca manual**: Use "CONEXÃO MANUAL" no menu
2. **IP correto**: Confirme o IP com o host
3. **Servidor ativo**: Verifique se o servidor está rodando

#### ❌ "Lag/Travamentos"
1. **Rede lenta**: Use cabo ethernet se possível
2. **Muitos jogadores**: Máximo recomendado é 4
3. **Hardware**: Feche outros programas

### 📊 Arquitetura Técnica

#### 🖥️ Servidor Dedicado
- **Linguagem**: Python com sockets TCP
- **Tick Rate**: 60 FPS
- **Estado**: Sincronizado entre todos os clientes
- **IA**: Processada no servidor

#### 📡 Protocolo de Rede
- **Formato**: JSON sobre TCP
- **Porta**: 12345 (padrão)
- **Mensagens**:
  - `join`: Entrar no jogo
  - `update_player`: Atualizar posição
  - `shoot`: Disparar projétil
  - `get_game_state`: Solicitar estado

#### 🔄 Sincronização
- **Posições**: Atualizadas em tempo real
- **Projéteis**: Criados no servidor
- **Colisões**: Calculadas no servidor
- **IA**: Executada no servidor

### 🚀 Comandos Úteis

#### 🖥️ Iniciar Servidor
```bash
# Servidor básico
python start_server.py

# Servidor com IP específico
python -c "from src.server import GameServer; GameServer('*************').start()"

# Servidor com porta personalizada
python -c "from src.server import GameServer; GameServer('0.0.0.0', 8080).start()"
```

#### 🔍 Descobrir Servidores
```bash
# Buscar servidores na rede
python -c "from src.network_client import ServerDiscovery; print(ServerDiscovery.find_servers())"

# Testar conexão específica
python -c "from src.network_client import ServerDiscovery; print(ServerDiscovery.test_server('*************', 12345))"
```

### 📝 Exemplo de Sessão

#### 1️⃣ **Host (Servidor)**
```bash
$ python start_server.py
============================================================
    ARENA BATTLE ROYALE - SERVIDOR DEDICADO
============================================================

IP local da máquina: *************
Outros jogadores devem conectar em: *************:12345

Iniciando servidor em 0.0.0.0:12345
Pressione Ctrl+C para parar o servidor

Aguardando jogadores...
----------------------------------------
Nova conexão de ('*************', 54321)
Jogador Player1 entrou (ID: 1)
Jogo iniciado!
```

#### 2️⃣ **Cliente 1**
```
1. Executar: python main.py
2. Digite nickname: "Player1"
3. Clique "MULTIPLAYER"
4. Servidor encontrado: *************:12345
5. Conectado! Aguardando outros jogadores...
```

#### 3️⃣ **Cliente 2**
```
1. Executar: python main.py
2. Digite nickname: "Player2"
3. Clique "MULTIPLAYER"
4. Conexão manual: *************:12345
5. Conectado! Jogo iniciado!
```

### 🎯 Dicas de Performance

#### 🚀 **Para o Host**
- Use **cabo ethernet** se possível
- **Feche programas** desnecessários
- **Mantenha o servidor** em primeiro plano

#### 📱 **Para Clientes**
- **Conexão estável** é mais importante que velocidade
- **Ping baixo** (< 50ms) é ideal
- **Evite downloads** durante o jogo

### 🔮 Funcionalidades Futuras

- [ ] **Chat de texto** entre jogadores
- [ ] **Modo PvP** (jogadores vs jogadores)
- [ ] **Salas privadas** com senha
- [ ] **Servidor dedicado** em nuvem
- [ ] **Ranking** e estatísticas
- [ ] **Replay** de partidas

---

## 🎮 **Divirta-se jogando Arena Battle Royale em multiplayer!**

Para suporte técnico ou dúvidas, verifique os logs do servidor e cliente.
